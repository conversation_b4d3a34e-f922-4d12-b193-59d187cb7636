#!/bin/bash

# Exit when any command fails
set -e

# Change to the script's directory
cd /var/www/micado-scraping

# Config git
export SSH_KEY_PATH="/home/<USER>/.ssh/id_rsa"
eval `ssh-agent -s`
ssh-add $SSH_KEY_PATH
git config --global --add safe.directory /var/www/micado-scraping

# Pull new source code
echo "Pulling new source code..."
git fetch origin dev
git checkout -f
git checkout dev
git pull origin dev

echo "Activating python virtual environment..."
# Config environment
. ./venv/bin/activate

echo "Installing dependencies..."
pip install -r requirements.txt

echo "run crawler..."
python /var/www/micado-scraping/src/rakuten/main.py

echo "\nDone."
exit 0
