# 🏨 Micado Scraping Project - Rakuten Travel Crawler

Hệ thống crawl dữ liệu khách sạn từ Rakuten Travel (楽天トラベル) - Website du lịch lớn nhất Nhật Bản.

## 📊 Tình Trạng Crawl Hiện Tại

### ✅ Đã Hoàn Thành (100%)
- **📍 Areas**: 13 main areas → 80 sub areas → 392 sub-sub areas
- **🏨 Hotels**: 12,223 hotels → 9,906 unique hotels
- **📋 Hotel Details**: 9,906 hotels (100%) - Plans, Rooms, Pricing
- **📦 Dung lượng**: 67.2 MB dữ liệu

### 🔄 Đang Tiến Hành
- **💬 Comments**: 10/9,906 hotels (0.1%) - ~40,793 comments đã crawl

## 🚀 Cài Đặt & Sử Dụng

### 1. Setup Environment
```bash
# Tạo virtual environment
python3.11 -m venv venv
source venv/bin/activate  # macOS/Linux
# venv\Scripts\activate   # Windows

# Cài đặt dependencies
pip install --upgrade pip
pip install -r requirements.txt
```

### 2. Crawl Dữ Liệu <PERSON> (<PERSON><PERSON>ành)
```bash
# Crawl toàn bộ: areas → hotels → details
python src/rakuten/crawler/start.py
```

### 3. Crawl Comments (Đang tiến hành)
```bash
# Crawl comments theo batch
python crawl_comments_batch.py --batch-size 50 --max-batches 10

# Crawl tất cả comments (chạy lâu)
python crawl_comments_batch.py --batch-size 100
```

### 4. Kiểm Tra Tình Trạng
```bash
# Báo cáo chi tiết
python crawl_status_report.py

# Fix missing files (nếu cần)
python fix_missing_files.py
```

## 📁 Cấu Trúc Project (Đã Dọn Dẹp)

```
📂 micado-scraping/
├── 📄 README.md                    # Tài liệu này
├── 📄 requirements.txt             # Dependencies
├── 📄 crawl_comments_batch.py      # 🔥 Main comment crawler
├── 📄 fix_missing_files.py         # Utility sửa lỗi
├── 📄 crawl_status_report.py       # Báo cáo tình trạng
├── 📂 src/
│   ├── 📄 config.py                # Cấu hình
│   ├── 📄 common.py                # Utilities chung
│   ├── 📄 utils.py                 # Helper functions
│   └── 📂 rakuten/crawler/
│       ├── 📄 start.py             # 🔥 Main crawler entry
│       ├── 📄 crawl_subarea.py     # Crawl khu vực
│       ├── 📄 crawl_hotel_list.py  # Crawl danh sách hotels
│       ├── 📄 crawl_detail_hotel.py # Crawl chi tiết hotels
│       └── 📄 crawl_simple_comments.py # Crawl comments
├── 📂 data/raw/rakuten/            # 🗄️ Dữ liệu đã crawl
└── 📂 logs/                        # Log files
```

## 📊 Dữ Liệu Đã Crawl

| File | Records | Mô Tả |
|------|---------|-------|
| `main_areas_file.csv` | 13 | Khu vực chính (Tokyo, Osaka...) |
| `list_subarea_file.csv` | 80 | Khu vực con |
| `list_subarea_detail_file.csv` | 392 | Khu vực chi tiết |
| `list_hotel_detail_url_file.csv` | 12,223 | Danh sách hotels (có duplicate) |
| `list_hotel_detail_url_file_unique.csv` | 9,906 | Hotels unique |
| `detail_hotel_file.csv` | 9,906 | Thông tin chi tiết hotels |
| `hotel_plans_file.csv` | 20,361 | Plans/packages |
| `hotel_rooms_file.csv` | 13,594 | Rooms/phòng |
| `simple_hotel_comments.csv` | 0 | Comments (đang crawl) |

## 🎯 Kế Hoạch Tiếp Theo

1. **🔄 Tiếp tục crawl comments** cho 9,896 hotels còn lại
2. **🧹 Làm sạch dữ liệu** và chuẩn hóa format
3. **📊 Phân tích dữ liệu** và tạo insights
4. **📈 Tạo dashboard** visualization

## ⚙️ Cấu Hình

### Crawler Settings
- **Delay**: 1-3 giây giữa các requests
- **Retry**: 3 lần cho mỗi failed request
- **Timeout**: 30 giây
- **User-Agent**: Rotation để tránh block

### Output Format
- **CSV**: Dữ liệu structured
- **Encoding**: UTF-8
- **Headers**: Tiếng Anh cho compatibility

## 🔧 Troubleshooting

### Lỗi Thường Gặp
1. **503 Service Unavailable**: Tăng delay, giảm batch size
2. **Missing files**: Chạy `python fix_missing_files.py`
3. **Memory issues**: Giảm batch size xuống 10-20

### Monitoring
```bash
# Theo dõi logs
tail -f logs/crawler_*.log

# Kiểm tra tiến độ
python crawl_status_report.py
```

## 📈 Thống Kê Hiệu Suất

- **Hotel Details**: 2,147 items/phút
- **Comments**: 28 pages/phút
- **Tổng thời gian crawl cơ bản**: ~1.5 giờ
- **Ước tính thời gian crawl comments**: ~20-30 giờ

## 🤝 Đóng Góp

Project đã được tối ưu và dọn dẹp. Các file không cần thiết đã được xóa để dễ bảo trì.
