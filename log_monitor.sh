#!/bin/bash
"""
Log Monitor for Incremental Crawler
Monitor and analyze daily crawler logs
"""

PROJECT_DIR="/var/www/micado-scraping"
LOG_DIR="$PROJECT_DIR/logs"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}📊 INCREMENTAL CRAWLER LOG MONITOR${NC}"
echo "========================================"

# Function to show log summary
show_log_summary() {
    echo -e "${BLUE}📋 LOG FILES SUMMARY${NC}"
    echo "--------------------"
    
    if [ -d "$LOG_DIR" ]; then
        echo "📁 Log directory: $LOG_DIR"
        echo "📊 Total log files: $(ls -1 $LOG_DIR/incremental_crawler_*.log 2>/dev/null | wc -l)"
        echo "💾 Directory size: $(du -sh $LOG_DIR 2>/dev/null | cut -f1)"
        echo ""
        
        echo "📅 Recent log files:"
        ls -lt $LOG_DIR/incremental_crawler_*.log 2>/dev/null | head -5 | while read line; do
            echo "   $line"
        done
    else
        echo "❌ Log directory not found: $LOG_DIR"
    fi
}

# Function to show latest log status
show_latest_status() {
    echo -e "${BLUE}🔍 LATEST CRAWL STATUS${NC}"
    echo "----------------------"
    
    if [ -f "$LOG_DIR/latest_incremental.log" ]; then
        echo "📄 Latest log: $LOG_DIR/latest_incremental.log"
        
        # Check for success/failure
        if grep -q "✅.*completed successfully" "$LOG_DIR/latest_incremental.log"; then
            echo -e "✅ Status: ${GREEN}SUCCESS${NC}"
        elif grep -q "❌.*failed" "$LOG_DIR/latest_incremental.log"; then
            echo -e "❌ Status: ${RED}FAILED${NC}"
        else
            echo -e "🔄 Status: ${YELLOW}RUNNING/UNKNOWN${NC}"
        fi
        
        # Extract key metrics
        echo ""
        echo "📊 Key metrics from latest run:"
        grep -E "(hotels|crawl|import)" "$LOG_DIR/latest_incremental.log" | tail -10 | while read line; do
            echo "   $line"
        done
        
    else
        echo "❌ No latest log found"
    fi
}

# Function to analyze errors
analyze_errors() {
    echo -e "${BLUE}🚨 ERROR ANALYSIS${NC}"
    echo "------------------"
    
    if [ -d "$LOG_DIR" ]; then
        # Count errors in recent logs
        error_count=$(grep -c -i "error\|failed\|exception" $LOG_DIR/incremental_crawler_*.log 2>/dev/null || echo "0")
        echo "🔍 Total errors found: $error_count"
        
        if [ "$error_count" -gt 0 ]; then
            echo ""
            echo "🚨 Recent errors:"
            grep -i "error\|failed\|exception" $LOG_DIR/incremental_crawler_*.log 2>/dev/null | tail -5 | while read line; do
                echo -e "   ${RED}$line${NC}"
            done
        else
            echo -e "✅ ${GREEN}No errors found in recent logs${NC}"
        fi
    fi
}

# Function to show log statistics
show_statistics() {
    echo -e "${BLUE}📈 CRAWL STATISTICS${NC}"
    echo "-------------------"
    
    if [ -d "$LOG_DIR" ]; then
        # Analyze recent successful runs
        echo "📊 Recent successful runs:"
        grep -l "completed successfully" $LOG_DIR/incremental_crawler_*.log 2>/dev/null | tail -5 | while read logfile; do
            date_str=$(basename "$logfile" | sed 's/incremental_crawler_\(.*\)\.log/\1/')
            hotels=$(grep -o "New hotels to crawl: [0-9]*" "$logfile" | tail -1 | grep -o "[0-9]*")
            echo "   📅 $date_str: $hotels hotels crawled"
        done
    fi
}

# Main menu
case "${1:-summary}" in
    "summary"|"")
        show_log_summary
        echo ""
        show_latest_status
        ;;
    "errors")
        analyze_errors
        ;;
    "stats")
        show_statistics
        ;;
    "tail")
        echo -e "${BLUE}📄 TAILING LATEST LOG${NC}"
        echo "---------------------"
        if [ -f "$LOG_DIR/latest_incremental.log" ]; then
            tail -f "$LOG_DIR/latest_incremental.log"
        else
            echo "❌ No latest log found"
        fi
        ;;
    "list")
        echo -e "${BLUE}📋 ALL LOG FILES${NC}"
        echo "----------------"
        ls -la "$LOG_DIR/"
        ;;
    *)
        echo "Usage: $0 [summary|errors|stats|tail|list]"
        echo ""
        echo "Commands:"
        echo "  summary  - Show log summary and latest status (default)"
        echo "  errors   - Analyze errors in logs"
        echo "  stats    - Show crawl statistics"
        echo "  tail     - Tail latest log file"
        echo "  list     - List all log files"
        ;;
esac
