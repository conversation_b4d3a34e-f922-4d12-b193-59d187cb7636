#!/usr/bin/env python3
"""
Crawl comments for hotels in batches
This allows better control and monitoring of the crawling process
"""

import sys
import os
import pandas as pd
import argparse
from datetime import datetime
from scrapy.crawler import CrawlerProcess

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.rakuten.crawler.crawl_simple_comments import SimpleHotelCommentsSpider

def create_batches(df, batch_size):
    """Split dataframe into batches"""
    batches = []
    for i in range(0, len(df), batch_size):
        batch = df.iloc[i:i + batch_size]
        batches.append(batch)
    return batches

def crawl_batch(batch_df, batch_num, total_batches):
    """Crawl comments for a single batch"""
    
    print(f"\n🔄 BATCH {batch_num}/{total_batches}")
    print("=" * 60)
    print(f"📊 Hotels in this batch: {len(batch_df)}")
    print(f"🏨 Hotel ID range: {batch_df['hotel_id'].min()} - {batch_df['hotel_id'].max()}")
    print(f"📅 Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create temporary file for this batch
    batch_file = f'./data/temp/batch_{batch_num}_hotels.csv'
    os.makedirs('./data/temp', exist_ok=True)
    batch_df.to_csv(batch_file, index=False, encoding='utf-8')
    
    # Create a custom spider for this batch
    class BatchCommentsSpider(SimpleHotelCommentsSpider):
        name = f'batch_comments_{batch_num}'
        
        def start_requests(self):
            """Override to use batch file"""
            import scrapy
            df = pd.read_csv(batch_file, encoding='utf-8')
            
            for _, row in df.iterrows():
                hotel_id = str(row['hotel_id'])
                url = f"https://travel.rakuten.co.jp/HOTEL/{hotel_id}/review.html"
                yield scrapy.Request(url=url, callback=self.parse_comments_page,
                                   meta={'hotel_id': hotel_id, 'page': 1})
    
    # Configure crawler
    process = CrawlerProcess({
        'LOG_LEVEL': 'INFO',
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
        'CONCURRENT_REQUESTS': 3,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
        'RETRY_TIMES': 3,
        'AUTOTHROTTLE_ENABLED': True,
        'AUTOTHROTTLE_START_DELAY': 1,
        'AUTOTHROTTLE_MAX_DELAY': 8,
        'AUTOTHROTTLE_TARGET_CONCURRENCY': 2.0,
    })
    
    try:
        # Run the batch
        process.crawl(BatchCommentsSpider)
        process.start()
        
        print(f"✅ Batch {batch_num} completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Batch {batch_num} failed: {e}")
        return False
    
    finally:
        # Clean up temporary file
        if os.path.exists(batch_file):
            os.remove(batch_file)

def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(description='Crawl hotel comments in batches')
    parser.add_argument('--batch-size', type=int, default=100, 
                       help='Number of hotels per batch (default: 100)')
    parser.add_argument('--start-batch', type=int, default=1,
                       help='Starting batch number (default: 1)')
    parser.add_argument('--max-batches', type=int, default=None,
                       help='Maximum number of batches to process (default: all)')
    parser.add_argument('--hotel-file', type=str, default='./data/raw/rakuten/detail_hotel_file.csv',
                       help='Hotel list file path')
    
    args = parser.parse_args()
    
    print("🎯 BATCH COMMENT CRAWLER")
    print("=" * 80)
    print(f"⚙️  Configuration:")
    print(f"   - Batch size: {args.batch_size} hotels")
    print(f"   - Start batch: {args.start_batch}")
    print(f"   - Max batches: {args.max_batches or 'All'}")
    print(f"   - Hotel file: {args.hotel_file}")
    print()
    
    # Load hotel list
    if not os.path.exists(args.hotel_file):
        print(f"❌ Hotel file not found: {args.hotel_file}")
        return False
    
    try:
        df = pd.read_csv(args.hotel_file, encoding='utf-8')
        print(f"📊 Total hotels: {len(df):,}")
    except Exception as e:
        print(f"❌ Error reading hotel file: {e}")
        return False
    
    # Create batches
    batches = create_batches(df, args.batch_size)
    total_batches = len(batches)
    
    print(f"📦 Total batches: {total_batches}")
    
    # Apply start batch and max batches filters
    start_idx = args.start_batch - 1
    if start_idx >= total_batches:
        print(f"❌ Start batch {args.start_batch} is beyond total batches {total_batches}")
        return False
    
    end_idx = total_batches
    if args.max_batches:
        end_idx = min(start_idx + args.max_batches, total_batches)
    
    batches_to_process = batches[start_idx:end_idx]
    
    print(f"🎯 Processing batches {args.start_batch} to {start_idx + len(batches_to_process)}")
    print(f"📊 Total hotels to process: {sum(len(batch) for batch in batches_to_process):,}")
    print()
    
    # Process batches
    successful_batches = 0
    failed_batches = 0
    
    for i, batch_df in enumerate(batches_to_process):
        batch_num = start_idx + i + 1
        
        try:
            if crawl_batch(batch_df, batch_num, total_batches):
                successful_batches += 1
            else:
                failed_batches += 1
                
        except KeyboardInterrupt:
            print(f"\n⚠️  Crawling interrupted by user at batch {batch_num}")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error in batch {batch_num}: {e}")
            failed_batches += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 BATCH CRAWLING SUMMARY")
    print("=" * 80)
    print(f"✅ Successful batches: {successful_batches}")
    print(f"❌ Failed batches: {failed_batches}")
    print(f"📅 End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Show results
    try:
        if os.path.exists('./data/raw/rakuten/simple_hotel_comments.csv'):
            result_df = pd.read_csv('./data/raw/rakuten/simple_hotel_comments.csv')
            print(f"📊 Total comments in file: {len(result_df):,}")
            print(f"🏨 Unique hotels with comments: {result_df['hotel_id'].nunique():,}")
    except Exception as e:
        print(f"⚠️  Could not read results: {e}")
    
    return successful_batches > 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Batch crawling completed!")
        else:
            print("\n❌ Batch crawling failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Crawling interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
