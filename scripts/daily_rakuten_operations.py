#!/usr/bin/env python3
"""
Main script for daily Rakuten Travel operations:
1. Crawl hotel data
2. Import to database
3. Generate reports
"""

import os
import sys
import argparse
import logging
import subprocess
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'rakuten_daily_{datetime.now().strftime("%Y%m%d")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a shell command and log output"""
    logger.info(f"🚀 Starting: {description}")
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
            text=True
        )
        stdout, stderr = process.communicate()

        if process.returncode == 0:
            logger.info(f"✅ Completed: {description}")
            logger.debug(f"Output: {stdout}")
            return True
        else:
            logger.error(f"❌ Failed: {description}")
            logger.error(f"Error: {stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Exception in {description}: {e}")
        return False

def crawl_hotels():
    """Run hotel crawler"""
    logger.info("=" * 50)
    logger.info("📊 STEP 1: CRAWLING HOTEL DATA")
    logger.info("=" * 50)

    # Step 1.1: Crawl main areas
    if not run_command(
        "python3 src/rakuten/crawler/start.py",
        "Crawling main areas"
    ):
        return False

    # Step 1.2: Crawl sub areas
    if not run_command(
        "python3 src/rakuten/crawler/crawl_subarea.py",
        "Crawling sub areas"
    ):
        return False

    # Step 1.3: Crawl sub-sub areas
    if not run_command(
        "python3 src/rakuten/crawler/crawl_sub_subarea.py",
        "Crawling sub-sub areas"
    ):
        return False

    # Step 1.4: Crawl hotel list
    if not run_command(
        "python3 scripts/daily/crawl_all_hotels.py",
        "Crawling hotel list"
    ):
        return False

    # Step 1.5: Crawl hotel details (optional)
    if run_command(
        "python3 src/rakuten/crawler/crawl_detail_hotel.py",
        "Crawling hotel details"
    ):
        logger.info("✅ Hotel detail crawling completed")
    else:
        logger.warning("⚠️ Hotel detail crawling failed, but continuing with import")

    # Step 1.6: Crawl hotel comments (optional but recommended)
    if run_command(
        "python3 crawl_comments_batch.py --batch-size 50",
        "Crawling ALL hotel comments"
    ):
        logger.info("✅ Hotel comment crawling completed")
    else:
        logger.warning("⚠️ Hotel comment crawling failed, but continuing with import")

    logger.info("✅ All crawling steps completed")
    return True

def import_hotels(import_mode):
    """Import hotels to database"""
    logger.info("=" * 50)
    logger.info("📊 STEP 2: IMPORTING HOTEL DATA")
    logger.info("=" * 50)

    # Run the advanced hotel importer
    if run_command(
        f"python3 scripts/daily/advanced_hotel_import.py --mode {import_mode}",
        f"Importing hotels ({import_mode} mode)"
    ):
        logger.info(f"✅ Hotel import completed ({import_mode} mode)")
        return True
    else:
        logger.error(f"❌ Hotel import failed ({import_mode} mode)")
        return False

def generate_reports():
    """Generate monitoring reports"""
    logger.info("=" * 50)
    logger.info("📊 STEP 3: GENERATING REPORTS")
    logger.info("=" * 50)

    # Run the monitoring script
    if run_command(
        "python3 scripts/monitoring/monitor_import_batches.py > logs/report_$(date +%Y%m%d).txt",
        "Generating monitoring report"
    ):
        logger.info("✅ Report generation completed")
        return True
    else:
        logger.error("❌ Report generation failed")
        return False

def main():
    """Main function to run daily operations"""
    parser = argparse.ArgumentParser(description='Rakuten Travel Daily Operations')
    parser.add_argument('--skip-crawl', action='store_true', help='Skip crawling step')
    parser.add_argument('--skip-import', action='store_true', help='Skip import step')
    parser.add_argument('--skip-report', action='store_true', help='Skip report generation')
    parser.add_argument('--import-mode', choices=['incremental', 'replace'], 
                        default='incremental', help='Import mode (default: incremental)')

    args = parser.parse_args()

    logger.info("🚀 STARTING RAKUTEN TRAVEL DAILY OPERATIONS")
    start_time = datetime.now()
    logger.info(f"Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    success = True

    # Step 1: Crawl hotel data
    if not args.skip_crawl:
        if not crawl_hotels():
            logger.error("❌ Crawling failed, stopping operations")
            success = False
    else:
        logger.info("⏩ Skipping crawl step")

    # Step 2: Import to database
    if not args.skip_import and success:
        if not import_hotels(args.import_mode):
            logger.error("❌ Import failed, stopping operations")
            success = False
    else:
        logger.info("⏩ Skipping import step")

    # Step 3: Generate reports
    if not args.skip_report and success:
        if not generate_reports():
            logger.error("❌ Report generation failed")
            success = False
    else:
        logger.info("⏩ Skipping report generation")

    # Finish
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Duration: {duration}")

    if success:
        logger.info("✅ ALL OPERATIONS COMPLETED SUCCESSFULLY")
        return 0
    else:
        logger.error("❌ OPERATIONS FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
