#!/bin/bash
"""
Script to setup daily cron job for Rakuten Travel operations
"""

# Get the absolute path of the project
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPT_PATH="$PROJECT_DIR/scripts/daily_rakuten_operations.py"
VENV_PATH="$PROJECT_DIR/venv/bin/activate"

echo "🔧 SETTING UP DAILY CRON JOB FOR RAKUTEN TRAVEL"
echo "================================================"
echo "Project directory: $PROJECT_DIR"
echo "Script path: $SCRIPT_PATH"
echo "Virtual env: $VENV_PATH"

# Create cron job entry
CRON_ENTRY="0 2 * * * cd $PROJECT_DIR && source $VENV_PATH && python3 $SCRIPT_PATH --import-mode incremental >> logs/cron_daily.log 2>&1"

echo ""
echo "📋 Cron job entry:"
echo "$CRON_ENTRY"

echo ""
echo "🚀 To setup the cron job, run:"
echo "1. Open crontab editor:"
echo "   crontab -e"
echo ""
echo "2. Add this line:"
echo "   $CRON_ENTRY"
echo ""
echo "3. Save and exit"
echo ""
echo "📅 This will run daily at 2:00 AM"
echo ""
echo "🔍 To check current cron jobs:"
echo "   crontab -l"
echo ""
echo "📊 To view logs:"
echo "   tail -f $PROJECT_DIR/logs/cron_daily.log"
echo "   tail -f $PROJECT_DIR/logs/rakuten_daily_YYYYMMDD.log"

# Create logs directory
mkdir -p "$PROJECT_DIR/logs"
echo ""
echo "✅ Logs directory created: $PROJECT_DIR/logs"

# Make script executable
chmod +x "$SCRIPT_PATH"
echo "✅ Script made executable: $SCRIPT_PATH"

echo ""
echo "🎯 MANUAL TESTING:"
echo "To test the script manually:"
echo "cd $PROJECT_DIR"
echo "source $VENV_PATH"
echo "python3 $SCRIPT_PATH --help"
echo ""
echo "🔧 DIFFERENT RUN OPTIONS:"
echo "# Full run (crawl + import + report):"
echo "python3 $SCRIPT_PATH"
echo ""
echo "# Skip crawling (only import + report):"
echo "python3 $SCRIPT_PATH --skip-crawl"
echo ""
echo "# Only import (skip crawl + report):"
echo "python3 $SCRIPT_PATH --skip-crawl --skip-report"
echo ""
echo "# Replace mode (delete all + import):"
echo "python3 $SCRIPT_PATH --import-mode replace"
