#!/usr/bin/env python3
"""
Configuration for daily Rakuten Travel operations
"""

import os
from datetime import datetime

# Project paths
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_DIR = os.path.join(PROJECT_ROOT, 'data', 'raw', 'rakuten')
LOGS_DIR = os.path.join(PROJECT_ROOT, 'logs')
SCRIPTS_DIR = os.path.join(PROJECT_ROOT, 'scripts')

# Ensure directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

# Daily operation settings
DAILY_CONFIG = {
    # Crawling settings
    'crawl': {
        'enabled': True,
        'timeout_minutes': 120,  # 2 hours timeout for crawling
        'retry_attempts': 3,
        'delay_between_requests': 1,  # seconds
    },
    
    # Import settings
    'import': {
        'enabled': True,
        'default_mode': 'incremental',  # 'incremental' or 'replace'
        'batch_size': 100,  # records per batch commit
        'timeout_minutes': 60,  # 1 hour timeout for import
    },
    
    # Monitoring settings
    'monitoring': {
        'enabled': True,
        'generate_reports': True,
        'alert_on_failure': True,
        'retention_days': 30,  # Keep logs for 30 days
    },
    
    # File paths
    'files': {
        'main_areas': os.path.join(DATA_DIR, 'main_areas_file.csv'),
        'sub_areas': os.path.join(DATA_DIR, 'list_subarea_file.csv'),
        'sub_sub_areas': os.path.join(DATA_DIR, 'list_subarea_detail_file.csv'),
        'hotel_list': os.path.join(DATA_DIR, 'list_hotel_detail_url_file.csv'),
        'hotel_list_cleaned': os.path.join(DATA_DIR, 'list_hotel_detail_url_file_cleaned.csv'),
        'hotel_details': os.path.join(DATA_DIR, 'detail_hotel_file.csv'),
        'hotel_rooms': os.path.join(DATA_DIR, 'hotel_rooms_file.csv'),
        'hotel_plans': os.path.join(DATA_DIR, 'hotel_plans_file.csv'),
        'hotel_plans_content': os.path.join(DATA_DIR, 'hotel_plans_cotent_file.csv'),
    },
    
    # Logging settings
    'logging': {
        'level': 'INFO',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'daily_log': os.path.join(LOGS_DIR, f'rakuten_daily_{datetime.now().strftime("%Y%m%d")}.log'),
        'cron_log': os.path.join(LOGS_DIR, 'cron_daily.log'),
        'error_log': os.path.join(LOGS_DIR, 'errors.log'),
    },
    
    # Email notifications (optional)
    'notifications': {
        'enabled': False,  # Set to True to enable email notifications
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'email_from': '',  # Set your email
        'email_to': [],    # List of recipient emails
        'email_password': '',  # Set your email password or app password
    },
    
    # Data quality checks
    'quality_checks': {
        'min_hotels_expected': 9000,  # Minimum number of hotels expected
        'max_error_rate': 0.05,       # Maximum 5% error rate
        'required_fields': ['name', 'area', 'in_site_id'],
    },
}

# Cron schedule options
CRON_SCHEDULES = {
    'daily_2am': '0 2 * * *',           # Every day at 2:00 AM
    'daily_3am': '0 3 * * *',           # Every day at 3:00 AM
    'weekly_sunday': '0 2 * * 0',       # Every Sunday at 2:00 AM
    'twice_daily': '0 2,14 * * *',      # Every day at 2:00 AM and 2:00 PM
}

# Environment-specific settings
ENVIRONMENTS = {
    'development': {
        'import_mode': 'incremental',
        'crawl_timeout': 30,
        'import_timeout': 15,
        'enable_notifications': False,
    },
    'staging': {
        'import_mode': 'incremental',
        'crawl_timeout': 60,
        'import_timeout': 30,
        'enable_notifications': False,
    },
    'production': {
        'import_mode': 'incremental',
        'crawl_timeout': 120,
        'import_timeout': 60,
        'enable_notifications': True,
    },
}

def get_config(environment='production'):
    """Get configuration for specific environment"""
    config = DAILY_CONFIG.copy()
    
    if environment in ENVIRONMENTS:
        env_config = ENVIRONMENTS[environment]
        config['import']['default_mode'] = env_config['import_mode']
        config['crawl']['timeout_minutes'] = env_config['crawl_timeout']
        config['import']['timeout_minutes'] = env_config['import_timeout']
        config['notifications']['enabled'] = env_config['enable_notifications']
    
    return config

def get_cron_schedule(schedule_name='daily_2am'):
    """Get cron schedule string"""
    return CRON_SCHEDULES.get(schedule_name, CRON_SCHEDULES['daily_2am'])

if __name__ == "__main__":
    # Print configuration for debugging
    import json
    config = get_config()
    print("📋 DAILY CONFIGURATION:")
    print(json.dumps(config, indent=2, default=str))
