# 🚀 Rakuten Travel Daily Operations

## 📋 Overview

This directory contains scripts and configurations for automated daily crawling and importing of Rakuten Travel hotel data.

## 📁 Directory Structure

```
scripts/
├── daily/
│   ├── advanced_hotel_import.py    # Advanced import system with timestamp tracking
│   └── crawl_all_hotels.py         # Hotel crawler script
├── monitoring/
│   ├── monitor_import_batches.py   # Batch monitoring and analysis
│   └── daily_dashboard.py          # Daily monitoring dashboard
├── config/
│   └── daily_config.py             # Configuration settings
├── daily_rakuten_operations.py     # Main orchestration script
├── setup_daily_cron.sh             # Cron job setup script
└── README_DAILY_OPERATIONS.md      # This file
```

## 🎯 Main Scripts

### 1. **daily_rakuten_operations.py** - Main Orchestration Script

**Purpose**: Orchestrates the complete daily workflow

**Usage**:
```bash
# Full daily run (crawl + import + report)
python3 scripts/daily_rakuten_operations.py

# Skip crawling (only import + report)
python3 scripts/daily_rakuten_operations.py --skip-crawl

# Only import (skip crawl + report)
python3 scripts/daily_rakuten_operations.py --skip-crawl --skip-report

# Use replace mode (delete all + import)
python3 scripts/daily_rakuten_operations.py --import-mode replace
```

**Features**:
- ✅ Complete workflow automation
- ✅ Error handling and logging
- ✅ Configurable steps (skip crawl/import/report)
- ✅ Support for incremental and replace modes
- ✅ Detailed logging with timestamps

### 2. **advanced_hotel_import.py** - Import System

**Purpose**: Advanced hotel import with timestamp tracking and batch management

**Usage**:
```bash
# Incremental import (update existing)
python3 scripts/daily/advanced_hotel_import.py --mode incremental

# Replace import (delete all + insert)
python3 scripts/daily/advanced_hotel_import.py --mode replace
```

**Features**:
- ✅ Incremental vs Replace modes
- ✅ Batch tracking with unique IDs
- ✅ Timestamp tracking (created_at, updated_at, imported_at)
- ✅ Error handling and retry logic
- ✅ Performance monitoring (records/minute)
- ✅ Data validation

### 3. **monitor_import_batches.py** - Monitoring System

**Purpose**: Monitor import batches and analyze data quality

**Usage**:
```bash
python3 scripts/monitoring/monitor_import_batches.py
```

**Features**:
- ✅ Import batch analysis
- ✅ Data quality metrics
- ✅ Timestamp analysis
- ✅ Source distribution
- ✅ Sample data display

### 4. **daily_dashboard.py** - Monitoring Dashboard

**Purpose**: Generate daily monitoring dashboard

**Usage**:
```bash
python3 scripts/monitoring/daily_dashboard.py
```

**Features**:
- ✅ Overall statistics
- ✅ Recent import activity
- ✅ Data quality metrics
- ✅ Top areas by hotel count
- ✅ System health check
- ✅ File system status
- ✅ Recommendations

## ⚙️ Setup Instructions

### 1. **Setup Cron Job for Daily Automation**

```bash
# Make setup script executable
chmod +x scripts/setup_daily_cron.sh

# Run setup script
./scripts/setup_daily_cron.sh
```

This will show you the cron entry to add for daily automation at 2:00 AM.

### 2. **Manual Cron Setup**

```bash
# Edit crontab
crontab -e

# Add this line for daily run at 2:00 AM
0 2 * * * cd /path/to/micado-scraping && source venv/bin/activate && python3 scripts/daily_rakuten_operations.py --import-mode incremental >> logs/cron_daily.log 2>&1
```

### 3. **Configuration**

Edit `scripts/config/daily_config.py` to customize:
- Timeout settings
- Batch sizes
- File paths
- Logging levels
- Email notifications
- Data quality thresholds

## 📊 Import Modes

### **Incremental Mode (Recommended for Daily)**
- ✅ **Logic**: INSERT new hotels, UPDATE existing hotels
- ✅ **Preserves**: Original created_at timestamps
- ✅ **Updates**: updated_at to current timestamp
- ✅ **Safe**: No data loss
- ✅ **Fast**: Only processes changes
- ❌ **Limitation**: Doesn't remove deleted hotels

### **Replace Mode (Use with Caution)**
- ⚠️ **Logic**: DELETE all hotels, INSERT all from CSV
- ❌ **Loses**: All original timestamps
- ✅ **Ensures**: 100% data consistency with source
- ❌ **Risk**: Data loss if import fails
- ❌ **Slow**: Processes all records

## 📈 Monitoring and Logs

### **Log Files**
```
logs/
├── rakuten_daily_YYYYMMDD.log    # Daily operation logs
├── cron_daily.log                # Cron job logs
├── errors.log                    # Error logs
└── report_YYYYMMDD.txt           # Daily reports
```

### **Monitoring Commands**
```bash
# View today's log
tail -f logs/rakuten_daily_$(date +%Y%m%d).log

# View cron logs
tail -f logs/cron_daily.log

# Generate dashboard
python3 scripts/monitoring/daily_dashboard.py

# Check import batches
python3 scripts/monitoring/monitor_import_batches.py
```

## 🔧 Troubleshooting

### **Common Issues**

1. **Import Fails**
   ```bash
   # Check logs
   tail -100 logs/rakuten_daily_$(date +%Y%m%d).log
   
   # Run manually to debug
   python3 scripts/daily_rakuten_operations.py --skip-crawl
   ```

2. **Crawling Fails**
   ```bash
   # Run only import
   python3 scripts/daily_rakuten_operations.py --skip-crawl
   
   # Check crawler individually
   python3 src/rakuten/crawler/start.py
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   python3 scripts/monitoring/monitor_import_batches.py
   ```

### **Recovery Procedures**

1. **If Daily Import Fails**
   - Check logs for specific error
   - Run manual import: `python3 scripts/daily/advanced_hotel_import.py --mode incremental`
   - If data corruption: `python3 scripts/daily/advanced_hotel_import.py --mode replace`

2. **If Crawling Fails**
   - Skip crawl and use existing data: `python3 scripts/daily_rakuten_operations.py --skip-crawl`
   - Run individual crawlers manually
   - Check network connectivity and rate limiting

## 📅 Recommended Schedule

- **Daily**: 2:00 AM (incremental import)
- **Weekly**: Sunday 2:00 AM (full crawl + incremental import)
- **Monthly**: 1st day 2:00 AM (replace import for data consistency)

## 🎯 Best Practices

1. **Always use incremental mode** for daily operations
2. **Monitor logs daily** for any issues
3. **Run dashboard weekly** to check system health
4. **Use replace mode sparingly** and only when necessary
5. **Keep logs for at least 30 days** for troubleshooting
6. **Test changes in development** before production deployment
