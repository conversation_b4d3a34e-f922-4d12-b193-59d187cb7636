#!/usr/bin/env python3
import sys
sys.path.append('./src')

import pandas as pd
from scrapy.crawler import CrawlerProcess
import scrapy
import re


class FullHotelSpider(scrapy.Spider):
    name = 'full_hotel_spider'

    def start_requests(self):
        print("🚀 Starting FULL hotel crawler...")
        
        # Read the area file
        df = pd.read_csv('./data/raw/rakuten/list_subarea_detail_file.csv')
        print(f"📊 Loaded {len(df)} area URLs to process")

        for i, (_, row) in enumerate(df.iterrows()):
            title = row['sub_area_2']
            url = row['url']
            if i % 50 == 0:  # Progress update every 50 URLs
                print(f"🔄 Processing {i+1}/{len(df)}: {title}")
            yield scrapy.Request(url=url, meta={'title': title}, callback=self.parse)

    def parse(self, response):
        title = response.meta.get('title', 'Unknown')
        url_list = response.url
        
        # Extract area from URL
        area = self.extract_area_from_url(url_list)
        
        # Look for hotel links - filter out empty ones and "more" links
        hotel_links = response.css('a[href*="/HOTEL/"]')
        
        hotel_count = 0
        for link in hotel_links:
            hotel_title = link.css('::text').get()
            hotel_url = link.css('::attr(href)').get()
            
            # Filter out empty titles and "more" links
            if (hotel_title and hotel_url and 
                hotel_title.strip() and 
                'もっと見る' not in hotel_title and
                'more' not in hotel_title.lower()):
                
                if not hotel_url.startswith('http'):
                    hotel_url = 'https://travel.rakuten.co.jp' + hotel_url
                
                hotel_count += 1
                yield {
                    'title_url_hotel': hotel_title.strip(),
                    'url': hotel_url,
                    'area': area,
                    'url_list': url_list
                }
        
        # Handle pagination
        if not self.is_last_page(response):
            next_page_links = response.css('li.pagingBack a')
            if next_page_links:
                next_page = next_page_links[0]
                yield response.follow(next_page, self.parse, meta={'title': title})

    def extract_area_from_url(self, url):
        """Extract area name from Rakuten travel URL"""
        try:
            # Pattern for extracting prefecture/area from URL
            match = re.search(r'/yado/([^/]+)/', url)
            if match:
                area = match.group(1)
                # Map common prefecture codes to names
                area_mapping = {
                    'tokyo': '東京', 'osaka': '大阪', 'kyoto': '京都', 'aichi': '愛知',
                    'kanagawa': '神奈川', 'hokkaido': '北海道', 'fukuoka': '福岡', 'okinawa': '沖縄',
                    'hiroshima': '広島', 'miyagi': '宮城', 'nagano': '長野', 'shizuoka': '静岡',
                    'chiba': '千葉', 'saitama': '埼玉', 'hyogo': '兵庫', 'ibaraki': '茨城',
                    'tochigi': '栃木', 'gunma': '群馬', 'niigata': '新潟', 'toyama': '富山',
                    'ishikawa': '石川', 'fukui': '福井', 'yamanashi': '山梨', 'gifu': '岐阜',
                    'mie': '三重', 'shiga': '滋賀', 'nara': '奈良', 'wakayama': '和歌山',
                    'tottori': '鳥取', 'shimane': '島根', 'okayama': '岡山', 'yamaguchi': '山口',
                    'tokushima': '徳島', 'kagawa': '香川', 'ehime': '愛媛', 'kochi': '高知',
                    'saga': '佐賀', 'nagasaki': '長崎', 'kumamoto': '熊本', 'oita': '大分',
                    'miyazaki': '宮崎', 'kagoshima': '鹿児島', 'fukushima': '福島',
                    'yamagata': '山形', 'iwate': '岩手', 'aomori': '青森', 'akita': '秋田'
                }
                return area_mapping.get(area, area)
            return 'Unknown'
        except:
            return 'Unknown'

    def is_last_page(self, response):
        """Check if this is the last page of pagination"""
        try:
            # Look for pagination indicators
            paging_to = response.css('li.pagingTo')
            if not paging_to:
                return True
            
            # Check if there's a "next" button
            next_links = response.css('li.pagingBack a')
            return len(next_links) == 0
        except:
            return True


if __name__ == "__main__":
    print("🎯 RAKUTEN HOTEL LIST CRAWLER - FULL VERSION")
    print("=" * 50)
    
    settings = {
        'FEEDS': {
            './data/raw/rakuten/list_hotel_detail_url_file.csv': {
                'format': 'csv',
                'overwrite': True
            }
        },
        'LOG_LEVEL': 'INFO',
        'CONCURRENT_REQUESTS': 16,
        'DOWNLOAD_DELAY': 0.5,
        'RANDOMIZE_DOWNLOAD_DELAY': 0.5
    }
    
    process = CrawlerProcess(settings=settings)
    process.crawl(FullHotelSpider)
    process.start()
    
    print("\n🎉 Full hotel crawling completed!")
    print("📁 Output saved to: ./data/raw/rakuten/list_hotel_detail_url_file.csv")
