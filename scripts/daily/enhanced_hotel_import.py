#!/usr/bin/env python3
"""
Enhanced Hotel Import System with New Hotel Detection
Tracks and reports newly added hotels vs updated existing hotels
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the existing advanced import system
from scripts.daily.advanced_hotel_import import AdvancedHotelImporter

class EnhancedHotelImporter(AdvancedHotelImporter):
    def __init__(self):
        super().__init__()
        self.new_hotels = []
        self.updated_hotels = []
        self.unchanged_hotels = []
    
    def hotel_exists_with_details(self, hotel_id: str):
        """Check if hotel exists and return detailed comparison data"""
        query = """
        SELECT id, name, address, area, rate, updated_at 
        FROM hotels 
        WHERE in_site_id = %s
        """
        self.cursor.execute(query, (hotel_id,))
        result = self.cursor.fetchone()
        
        if result:
            return {
                'id': result[0],
                'name': result[1],
                'address': result[2],
                'area': result[3],
                'rate': result[4],
                'updated_at': result[5]
            }
        return None
    
    def has_hotel_changed(self, hotel_data: dict, existing_data: dict) -> bool:
        """Check if hotel data has actually changed"""
        # Compare key fields
        fields_to_compare = ['hotel_name', 'hotel_address', 'area', 'rate']
        existing_fields = ['name', 'address', 'area', 'rate']
        
        for new_field, existing_field in zip(fields_to_compare, existing_fields):
            new_value = str(hotel_data.get(new_field, '')).strip()
            existing_value = str(existing_data.get(existing_field, '')).strip()
            
            if new_value != existing_value:
                return True
        
        return False
    
    def insert_hotel_enhanced(self, hotel_data: dict) -> bool:
        """Insert new hotel and track as new"""
        success = self.insert_hotel(hotel_data)
        if success:
            self.new_hotels.append({
                'hotel_id': hotel_data['hotel_id'],
                'name': hotel_data['hotel_name'],
                'area': hotel_data['area'],
                'action': 'INSERT_NEW'
            })
        return success
    
    def update_hotel_enhanced(self, hotel_id: str, hotel_data: dict, existing_data: dict) -> bool:
        """Update existing hotel and track changes"""
        # Check if data actually changed
        if self.has_hotel_changed(hotel_data, existing_data):
            success = self.update_hotel(hotel_id, hotel_data, existing_data)
            if success:
                self.updated_hotels.append({
                    'hotel_id': hotel_data['hotel_id'],
                    'name': hotel_data['hotel_name'],
                    'area': hotel_data['area'],
                    'action': 'UPDATE_CHANGED'
                })
            return success
        else:
            # No changes, just update batch info
            update_query = """
            UPDATE hotels SET
                import_batch_id = %s,
                last_crawled_at = %s
            WHERE in_site_id = %s
            """
            self.cursor.execute(update_query, (
                self.batch_id,
                datetime.now(),
                hotel_data['hotel_id']
            ))
            
            self.unchanged_hotels.append({
                'hotel_id': hotel_data['hotel_id'],
                'name': hotel_data['hotel_name'],
                'area': hotel_data['area'],
                'action': 'NO_CHANGE'
            })
            return True
    
    def import_hotels_enhanced(self, csv_file: str, import_mode: str = 'incremental') -> bool:
        """Enhanced import with detailed tracking"""
        try:
            self.import_stats['start_time'] = datetime.now()
            
            # Load hotel data
            self.logger.info(f'📂 Loading hotel data from {csv_file}...')
            import pandas as pd
            df_hotels = pd.read_csv(csv_file)
            self.import_stats['total_records'] = len(df_hotels)
            self.logger.info(f'   - Loaded {len(df_hotels):,} hotel records')
            
            # Create batch record
            notes = f"Enhanced import mode: {import_mode}, Source: {csv_file}"
            self.create_batch_record('hotels_enhanced', csv_file, notes)
            
            # Handle replace mode
            if import_mode == 'replace':
                self.logger.info('🗑️  Replace mode: Clearing existing hotel data...')
                self.cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
                self.cursor.execute("DELETE FROM hotels;")
                self.cursor.execute("ALTER TABLE hotels AUTO_INCREMENT = 1;")
                self.cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
                self.connection.commit()
                self.logger.info('   - Existing hotel data cleared')
            
            # Process hotels with enhanced tracking
            self.logger.info(f'📥 Processing hotels in enhanced {import_mode} mode...')
            
            for idx, row in df_hotels.iterrows():
                # Process hotel data
                hotel_data = self.process_hotel_data(row)
                if not hotel_data:
                    self.import_stats['error_records'] += 1
                    continue
                
                if import_mode == 'incremental':
                    existing_hotel = self.hotel_exists_with_details(hotel_data['hotel_id'])
                    
                    if existing_hotel:
                        # Update existing hotel with change detection
                        self.update_hotel_enhanced(hotel_data['hotel_id'], hotel_data, existing_hotel)
                    else:
                        # Insert new hotel
                        self.insert_hotel_enhanced(hotel_data)
                else:
                    # Replace mode: always insert as new
                    self.insert_hotel_enhanced(hotel_data)
                
                # Commit every 100 records
                if (idx + 1) % 100 == 0:
                    self.connection.commit()
                    self.logger.info(f'   - Processed {idx + 1:,} hotels (New: {len(self.new_hotels):,}, Updated: {len(self.updated_hotels):,}, Unchanged: {len(self.unchanged_hotels):,})...')
            
            # Final commit
            self.connection.commit()
            self.import_stats['end_time'] = datetime.now()
            
            # Update batch record
            self.update_batch_record('completed')
            
            # Show enhanced results
            self.show_enhanced_results()
            
            return True
            
        except Exception as e:
            self.logger.error(f'❌ Enhanced import error: {e}')
            if self.batch_id:
                self.update_batch_record('failed')
            return False
    
    def show_enhanced_results(self):
        """Display enhanced import results with new hotel tracking"""
        duration = self.import_stats['end_time'] - self.import_stats['start_time']
        total_processed = len(self.new_hotels) + len(self.updated_hotels) + len(self.unchanged_hotels)
        
        self.logger.info('\n📊 ENHANCED IMPORT RESULTS:')
        self.logger.info('=' * 60)
        self.logger.info(f'   - Batch ID: {self.batch_id}')
        self.logger.info(f'   - Start time: {self.import_stats["start_time"].strftime("%Y-%m-%d %H:%M:%S")}')
        self.logger.info(f'   - End time: {self.import_stats["end_time"].strftime("%Y-%m-%d %H:%M:%S")}')
        self.logger.info(f'   - Duration: {duration}')
        self.logger.info(f'   - Total records processed: {total_processed:,}')
        
        # Detailed breakdown
        self.logger.info(f'\n🆕 NEW HOTELS ADDED:')
        self.logger.info(f'   - Count: {len(self.new_hotels):,}')
        if self.new_hotels:
            self.logger.info(f'   - Sample new hotels:')
            for hotel in self.new_hotels[:5]:
                self.logger.info(f'     * {hotel["name"][:40]}... (ID: {hotel["hotel_id"]}, Area: {hotel["area"][:20]}...)')
        
        self.logger.info(f'\n🔄 EXISTING HOTELS UPDATED:')
        self.logger.info(f'   - Count: {len(self.updated_hotels):,}')
        if self.updated_hotels:
            self.logger.info(f'   - Sample updated hotels:')
            for hotel in self.updated_hotels[:5]:
                self.logger.info(f'     * {hotel["name"][:40]}... (ID: {hotel["hotel_id"]}, Area: {hotel["area"][:20]}...)')
        
        self.logger.info(f'\n⚪ HOTELS UNCHANGED:')
        self.logger.info(f'   - Count: {len(self.unchanged_hotels):,}')
        
        # Performance metrics
        if total_processed > 0:
            rate = total_processed / duration.total_seconds() * 60
            self.logger.info(f'\n⚡ PERFORMANCE:')
            self.logger.info(f'   - Processing rate: {rate:.1f} records/minute')
            self.logger.info(f'   - New hotel rate: {(len(self.new_hotels)/total_processed*100):.1f}%')
            self.logger.info(f'   - Update rate: {(len(self.updated_hotels)/total_processed*100):.1f}%')
            self.logger.info(f'   - Unchanged rate: {(len(self.unchanged_hotels)/total_processed*100):.1f}%')
        
        # Export new hotels list
        if self.new_hotels:
            self.export_new_hotels_list()
    
    def export_new_hotels_list(self):
        """Export list of new hotels to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'logs/new_hotels_{timestamp}.json'
        
        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        
        export_data = {
            'batch_id': self.batch_id,
            'import_date': self.import_stats['start_time'].isoformat(),
            'total_new_hotels': len(self.new_hotels),
            'new_hotels': self.new_hotels
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f'💾 New hotels list exported: {filename}')
        return filename

def main():
    """Main function to run enhanced hotel import"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced Hotel Import System with New Hotel Detection')
    parser.add_argument('--file', '-f', default='data/raw/rakuten/detail_hotel_file.csv',
                       help='CSV file to import (default: data/raw/rakuten/detail_hotel_file.csv)')
    parser.add_argument('--mode', '-m', choices=['incremental', 'replace'], default='incremental',
                       help='Import mode: incremental (update existing) or replace (delete all first)')
    
    args = parser.parse_args()
    
    # Create enhanced importer instance
    importer = EnhancedHotelImporter()
    
    try:
        # Connect to database
        if not importer.connect_database():
            return False
        
        # Import hotels with enhanced tracking
        success = importer.import_hotels_enhanced(args.file, args.mode)
        
        if success:
            importer.logger.info('✅ Enhanced hotel import completed successfully!')
            if importer.new_hotels:
                importer.logger.info(f'🎉 Found {len(importer.new_hotels):,} new hotels!')
        else:
            importer.logger.error('❌ Enhanced hotel import failed!')
        
        return success
        
    finally:
        # Always disconnect
        importer.disconnect_database()

if __name__ == "__main__":
    main()
