#!/usr/bin/env python3
"""
Advanced Hotel Import System with Timestamp Tracking and Batch Management
"""

import pymysql
from sshtunnel import SSHTunnelForwarder
import pandas as pd
import re
import os
import json
from datetime import datetime
from dotenv import load_dotenv
import logging
from typing import Dict, List, Optional, Tuple

load_dotenv()

# Database configuration
SSH_HOST = os.getenv('SSH_HOST')
SSH_USERNAME = os.getenv('SSH_USERNAME')
SSH_PORT = 22
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_NAME')
SSH_KEY_NAME = os.getenv('SSH_KEY_NAME')
SSH_PKEY = os.path.join('src/rakuten', SSH_KEY_NAME)

HOTEL_TYPE = 'Rakuten'
POSTAL_CODE_REGEX = r'〒(\d{3}-\d{4})'

class AdvancedHotelImporter:
    def __init__(self):
        self.tunnel = None
        self.connection = None
        self.cursor = None
        self.batch_id = None
        self.import_stats = {
            'total_records': 0,
            'imported_records': 0,
            'updated_records': 0,
            'skipped_records': 0,
            'error_records': 0,
            'start_time': None,
            'end_time': None
        }
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'hotel_import_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def connect_database(self):
        """Connect to database via SSH tunnel"""
        try:
            # Open SSH tunnel
            self.logger.info('🔗 Opening SSH tunnel...')
            self.tunnel = SSHTunnelForwarder(
                (SSH_HOST, SSH_PORT),
                ssh_username=SSH_USERNAME,
                ssh_pkey=SSH_PKEY,
                remote_bind_address=(DB_HOST, 3306)
            )
            self.tunnel.start()
            self.logger.info(f'   - SSH tunnel opened on port {self.tunnel.local_bind_port}')
            
            # Connect to database
            self.logger.info('🗄️  Connecting to database...')
            self.connection = pymysql.connect(
                host='127.0.0.1',
                user=DB_USER,
                passwd=DB_PASSWORD,
                db=DB_NAME,
                port=self.tunnel.local_bind_port,
                autocommit=False
            )
            self.cursor = self.connection.cursor()
            self.logger.info('   - Database connected successfully')
            return True
            
        except Exception as e:
            self.logger.error(f'❌ Database connection error: {e}')
            return False
    
    def disconnect_database(self):
        """Disconnect from database and close tunnel"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        if self.tunnel:
            self.tunnel.close()
        self.logger.info('🔌 Database disconnected')
    
    def create_batch_record(self, import_type: str, source_file: str, notes: str = None) -> str:
        """Create a new import batch record"""
        self.batch_id = f"{import_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        insert_query = """
        INSERT INTO import_batches (
            batch_id, import_type, start_time, status, source_file, notes
        ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        self.cursor.execute(insert_query, (
            self.batch_id, import_type, datetime.now(), 'running', source_file, notes
        ))
        self.connection.commit()
        
        self.logger.info(f'📊 Created batch record: {self.batch_id}')
        return self.batch_id
    
    def update_batch_record(self, status: str):
        """Update batch record with final statistics"""
        update_query = """
        UPDATE import_batches SET
            end_time = %s,
            status = %s,
            total_records = %s,
            imported_records = %s,
            skipped_records = %s,
            error_records = %s,
            updated_at = %s
        WHERE batch_id = %s
        """
        
        self.cursor.execute(update_query, (
            datetime.now(), status,
            self.import_stats['total_records'],
            self.import_stats['imported_records'] + self.import_stats['updated_records'],
            self.import_stats['skipped_records'],
            self.import_stats['error_records'],
            datetime.now(),
            self.batch_id
        ))
        self.connection.commit()
        
        self.logger.info(f'📊 Updated batch record: {self.batch_id} - Status: {status}')
    
    def extract_postal_code(self, address: str) -> str:
        """Extract postal code from address"""
        if not address:
            return ''
        
        postcode_pattern = re.compile(POSTAL_CODE_REGEX)
        match = postcode_pattern.search(address)
        if match:
            return match.group(1)
        return ''
    
    def process_hotel_data(self, row: pd.Series) -> Dict:
        """Process and validate hotel data"""
        try:
            # Extract and clean data
            hotel_data = {
                'hotel_id': str(row['hotel_id']).strip(),
                'hotel_name': str(row['hotel_name']).strip(),
                'hotel_address': str(row['hotel_address']).strip() if pd.notna(row['hotel_address']) else '',
                'number_of_room': 0,
                'rate': str(row['rate']).strip() if pd.notna(row['rate']) else '',
                'area': str(row['area']).strip() if pd.notna(row['area']) else '',
                'url': str(row['url']).strip() if pd.notna(row['url']) else ''
            }
            
            # Process number_of_room
            if pd.notna(row['number_of_room']) and str(row['number_of_room']).strip():
                try:
                    room_str = str(row['number_of_room']).replace(',', '').split('室')[0]
                    hotel_data['number_of_room'] = int(room_str)
                except:
                    hotel_data['number_of_room'] = 0
            
            # Extract postal code
            hotel_data['postal_code'] = self.extract_postal_code(hotel_data['hotel_address'])
            
            return hotel_data
            
        except Exception as e:
            self.logger.error(f'❌ Error processing hotel data: {e}')
            return None
    
    def hotel_exists(self, hotel_id: str) -> Optional[Dict]:
        """Check if hotel exists and return its data"""
        query = "SELECT id, updated_at FROM hotels WHERE in_site_id = %s"
        self.cursor.execute(query, (hotel_id,))
        result = self.cursor.fetchone()
        
        if result:
            return {'id': result[0], 'updated_at': result[1]}
        return None
    
    def insert_hotel(self, hotel_data: Dict) -> bool:
        """Insert new hotel record"""
        try:
            insert_query = """
            INSERT INTO hotels (
                name, address, number_of_room, type, in_site_id, area, 
                postal_code, rate, is_active, is_user_registered,
                imported_at, import_batch_id, import_source, data_version,
                last_crawled_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            current_time = datetime.now()
            self.cursor.execute(insert_query, (
                hotel_data['hotel_name'],
                hotel_data['hotel_address'],
                hotel_data['number_of_room'],
                HOTEL_TYPE,
                hotel_data['hotel_id'],
                hotel_data['area'],
                hotel_data['postal_code'],
                hotel_data['rate'],
                True,
                0,
                current_time,
                self.batch_id,
                'rakuten_crawler_advanced',
                '2.0',
                current_time
            ))
            
            self.import_stats['imported_records'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f'❌ Error inserting hotel {hotel_data["hotel_id"]}: {e}')
            self.import_stats['error_records'] += 1
            return False
    
    def update_hotel(self, hotel_id: str, hotel_data: Dict, existing_data: Dict) -> bool:
        """Update existing hotel record"""
        try:
            update_query = """
            UPDATE hotels SET
                name = %s, address = %s, number_of_room = %s, area = %s,
                postal_code = %s, rate = %s, import_batch_id = %s,
                data_version = %s, last_crawled_at = %s
            WHERE in_site_id = %s
            """
            
            self.cursor.execute(update_query, (
                hotel_data['hotel_name'],
                hotel_data['hotel_address'],
                hotel_data['number_of_room'],
                hotel_data['area'],
                hotel_data['postal_code'],
                hotel_data['rate'],
                self.batch_id,
                '2.0',
                datetime.now(),
                hotel_data['hotel_id']
            ))
            
            self.import_stats['updated_records'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f'❌ Error updating hotel {hotel_data["hotel_id"]}: {e}')
            self.import_stats['error_records'] += 1
            return False

    def import_hotels(self, csv_file: str, import_mode: str = 'incremental') -> bool:
        """
        Import hotels from CSV file

        Args:
            csv_file: Path to CSV file
            import_mode: 'incremental' (update existing) or 'replace' (delete all first)
        """
        try:
            self.import_stats['start_time'] = datetime.now()

            # Load hotel data
            self.logger.info(f'📂 Loading hotel data from {csv_file}...')
            df_hotels = pd.read_csv(csv_file)
            self.import_stats['total_records'] = len(df_hotels)
            self.logger.info(f'   - Loaded {len(df_hotels):,} hotel records')

            # Create batch record
            notes = f"Import mode: {import_mode}, Source: {csv_file}"
            self.create_batch_record('hotels_advanced', csv_file, notes)

            # Handle replace mode
            if import_mode == 'replace':
                self.logger.info('🗑️  Replace mode: Clearing existing hotel data...')
                self.cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
                self.cursor.execute("DELETE FROM hotels;")
                self.cursor.execute("ALTER TABLE hotels AUTO_INCREMENT = 1;")
                self.cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
                self.connection.commit()
                self.logger.info('   - Existing hotel data cleared')

            # Process hotels
            self.logger.info(f'📥 Processing hotels in {import_mode} mode...')

            for idx, row in df_hotels.iterrows():
                # Process hotel data
                hotel_data = self.process_hotel_data(row)
                if not hotel_data:
                    self.import_stats['error_records'] += 1
                    continue

                # Check if hotel exists (only in incremental mode)
                if import_mode == 'incremental':
                    existing_hotel = self.hotel_exists(hotel_data['hotel_id'])

                    if existing_hotel:
                        # Update existing hotel
                        self.update_hotel(hotel_data['hotel_id'], hotel_data, existing_hotel)
                    else:
                        # Insert new hotel
                        self.insert_hotel(hotel_data)
                else:
                    # Replace mode: always insert
                    self.insert_hotel(hotel_data)

                # Commit every 100 records
                if (idx + 1) % 100 == 0:
                    self.connection.commit()
                    total_processed = self.import_stats['imported_records'] + self.import_stats['updated_records']
                    self.logger.info(f'   - Processed {idx + 1:,} hotels (Imported: {self.import_stats["imported_records"]:,}, Updated: {self.import_stats["updated_records"]:,})...')

            # Final commit
            self.connection.commit()
            self.import_stats['end_time'] = datetime.now()

            # Update batch record
            self.update_batch_record('completed')

            # Show results
            self.show_import_results()

            return True

        except Exception as e:
            self.logger.error(f'❌ Import error: {e}')
            if self.batch_id:
                self.update_batch_record('failed')
            return False

    def show_import_results(self):
        """Display import results"""
        duration = self.import_stats['end_time'] - self.import_stats['start_time']
        total_processed = self.import_stats['imported_records'] + self.import_stats['updated_records']

        self.logger.info('\n📊 IMPORT RESULTS:')
        self.logger.info('=' * 50)
        self.logger.info(f'   - Batch ID: {self.batch_id}')
        self.logger.info(f'   - Start time: {self.import_stats["start_time"].strftime("%Y-%m-%d %H:%M:%S")}')
        self.logger.info(f'   - End time: {self.import_stats["end_time"].strftime("%Y-%m-%d %H:%M:%S")}')
        self.logger.info(f'   - Duration: {duration}')
        self.logger.info(f'   - Total records: {self.import_stats["total_records"]:,}')
        self.logger.info(f'   - New hotels imported: {self.import_stats["imported_records"]:,}')
        self.logger.info(f'   - Existing hotels updated: {self.import_stats["updated_records"]:,}')
        self.logger.info(f'   - Records skipped: {self.import_stats["skipped_records"]:,}')
        self.logger.info(f'   - Records with errors: {self.import_stats["error_records"]:,}')
        self.logger.info(f'   - Success rate: {(total_processed/self.import_stats["total_records"]*100):.1f}%')

        if total_processed > 0:
            rate = total_processed / duration.total_seconds() * 60
            self.logger.info(f'   - Processing rate: {rate:.1f} records/minute')

        # Verify database count
        self.cursor.execute('SELECT COUNT(*) FROM hotels')
        total_in_db = self.cursor.fetchone()[0]
        self.logger.info(f'   - Total hotels in database: {total_in_db:,}')

        # Show sample imported data
        self.cursor.execute('''
            SELECT name, area, in_site_id, rate, created_at, import_batch_id
            FROM hotels
            WHERE import_batch_id = %s
            LIMIT 5
        ''', (self.batch_id,))
        sample_hotels = self.cursor.fetchall()

        if sample_hotels:
            self.logger.info('\n🏨 Sample processed hotels:')
            for hotel in sample_hotels:
                self.logger.info(f'   - {hotel[0][:30]}... | Area: {hotel[1][:20]}... | ID: {hotel[2]} | Rate: {hotel[3]} | Created: {hotel[4]} | Batch: {hotel[5]}')

def main():
    """Main function to run advanced hotel import"""
    import argparse

    parser = argparse.ArgumentParser(description='Advanced Hotel Import System')
    parser.add_argument('--file', '-f', default='data/raw/rakuten/detail_hotel_file.csv',
                       help='CSV file to import (default: data/raw/rakuten/detail_hotel_file.csv)')
    parser.add_argument('--mode', '-m', choices=['incremental', 'replace'], default='incremental',
                       help='Import mode: incremental (update existing) or replace (delete all first)')

    args = parser.parse_args()

    # Create importer instance
    importer = AdvancedHotelImporter()

    try:
        # Connect to database
        if not importer.connect_database():
            return False

        # Import hotels
        success = importer.import_hotels(args.file, args.mode)

        if success:
            importer.logger.info('✅ Hotel import completed successfully!')
        else:
            importer.logger.error('❌ Hotel import failed!')

        return success

    finally:
        # Always disconnect
        importer.disconnect_database()

if __name__ == "__main__":
    main()
