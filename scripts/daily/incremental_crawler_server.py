#!/usr/bin/env python3
"""
Incremental Rakuten Hotel Crawler - Fixed Version
Only crawls new/updated hotels, preserves existing data
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
import csv
import scrapy
import shutil

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.common import detail_hotel_file, hotel_rooms_file, hotel_plans_file, hotel_plans_cotent_file, UTF_8_ENCODING
from scrapy.crawler import CrawlerProcess

class IncrementalCrawler:
    def __init__(self):
        self.existing_hotels = set()
        self.new_hotels = []
        self.updated_hotels = []
        self.backup_dir = f"data/backup/{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def load_existing_hotels(self):
        """Load existing hotel IDs from current data"""
        try:
            if os.path.exists(detail_hotel_file):
                df = pd.read_csv(detail_hotel_file, encoding=UTF_8_ENCODING)
                self.existing_hotels = set(df['hotel_id'].astype(str))
                print(f"📊 Loaded {len(self.existing_hotels):,} existing hotels")
            else:
                print("📂 No existing hotel data found - will crawl all hotels")
        except Exception as e:
            print(f"⚠️ Error loading existing hotels: {e}")
            
    def backup_existing_data(self):
        """Backup existing data files"""
        os.makedirs(self.backup_dir, exist_ok=True)
        
        files_to_backup = [
            detail_hotel_file,
            hotel_rooms_file, 
            hotel_plans_file,
            hotel_plans_cotent_file
        ]
        
        for file_path in files_to_backup:
            if os.path.exists(file_path):
                backup_path = os.path.join(self.backup_dir, os.path.basename(file_path))
                shutil.copy2(file_path, backup_path)
                print(f"💾 Backed up: {file_path} -> {backup_path}")
                
    def get_hotels_to_crawl(self, force_update_days=7):
        """Get list of hotels that need crawling"""
        # Try different hotel list files
        possible_files = [
            './data/raw/rakuten/list_hotel_detail_url_file_unique.csv',
            './data/raw/rakuten/list_url_detail_hotel_file.csv'
        ]
        
        hotel_list_file = None
        for file_path in possible_files:
            if os.path.exists(file_path):
                hotel_list_file = file_path
                print(f"📂 Using hotel list: {file_path}")
                break
        
        if not hotel_list_file:
            print(f"❌ No hotel list file found. Tried: {possible_files}")
            return pd.DataFrame()  # Return empty DataFrame instead of list
            
        try:
            df_all_hotels = pd.read_csv(hotel_list_file, encoding=UTF_8_ENCODING)
            
            # Extract hotel IDs from URLs (handle different URL formats)
            if 'url' in df_all_hotels.columns:
                df_all_hotels['hotel_id'] = df_all_hotels['url'].str.extract(r'/HOTEL/(\d+)/')
            elif 'hotel_url' in df_all_hotels.columns:
                df_all_hotels['hotel_id'] = df_all_hotels['hotel_url'].str.extract(r'/HOTEL/(\d+)/')
                df_all_hotels['url'] = df_all_hotels['hotel_url']  # Standardize column name
            else:
                print(f"❌ No URL column found in {hotel_list_file}")
                return pd.DataFrame()
            
            # Remove rows with missing hotel_id
            df_all_hotels = df_all_hotels.dropna(subset=['hotel_id'])
            
            # Find new hotels (not in existing data)
            new_hotel_mask = ~df_all_hotels['hotel_id'].isin(self.existing_hotels)
            new_hotels_df = df_all_hotels[new_hotel_mask]
            
            # Find hotels to force update (older than X days)
            update_hotels_df = pd.DataFrame()
            if os.path.exists(detail_hotel_file):
                existing_df = pd.read_csv(detail_hotel_file, encoding=UTF_8_ENCODING)
                if 'last_crawled' in existing_df.columns:
                    cutoff_date = datetime.now() - timedelta(days=force_update_days)
                    old_hotels = existing_df[
                        pd.to_datetime(existing_df['last_crawled'], errors='coerce') < cutoff_date
                    ]['hotel_id'].astype(str)
                    
                    update_mask = df_all_hotels['hotel_id'].isin(old_hotels)
                    update_hotels_df = df_all_hotels[update_mask]
            
            # Combine new and update lists
            hotels_to_crawl = pd.concat([new_hotels_df, update_hotels_df]).drop_duplicates()
            
            print(f"🆕 New hotels to crawl: {len(new_hotels_df):,}")
            print(f"🔄 Hotels to update: {len(update_hotels_df):,}")
            print(f"📊 Total hotels to crawl: {len(hotels_to_crawl):,}")
            
            return hotels_to_crawl
            
        except Exception as e:
            print(f"❌ Error processing hotel list: {e}")
            return pd.DataFrame()
        
    def run_incremental_crawl(self, force_update_days=7):
        """Run incremental crawl"""
        print("🚀 Starting Incremental Rakuten Hotel Crawler...")
        print("=" * 60)
        
        # Step 1: Load existing data
        self.load_existing_hotels()
        
        # Step 2: Backup existing data
        if self.existing_hotels:
            self.backup_existing_data()
        
        # Step 3: Get hotels to crawl
        hotels_to_crawl = self.get_hotels_to_crawl(force_update_days)
        
        if hotels_to_crawl.empty:
            print("✅ No new hotels to crawl. All data is up to date!")
            return True
            
        # Step 4: Save incremental hotel list
        incremental_file = './data/temp/incremental_hotels.csv'
        os.makedirs(os.path.dirname(incremental_file), exist_ok=True)
        hotels_to_crawl.to_csv(incremental_file, index=False, encoding=UTF_8_ENCODING)
        
        # Step 5: Run crawler with incremental mode
        print(f"🔄 Starting crawler for {len(hotels_to_crawl):,} hotels...")
        
        try:
            # Import and run the crawler
            from src.rakuten.crawler.crawl_detail_hotel import DetailHotelSpider
            
            # Create a custom spider class for incremental crawling
            class IncrementalSpider(DetailHotelSpider):
                name = 'incremental_detail_hotel'
                
                def start_requests(self):
                    # Use incremental hotel list
                    df = pd.read_csv(incremental_file, encoding=UTF_8_ENCODING)
                    
                    # Prepare files for append mode
                    self.prepare_files_for_append()
                    
                    for _, row in df.iterrows():
                        url = row['url']
                        new_url = f"{url.split('.html')[0]}_std.html"
                        yield scrapy.Request(url=new_url, callback=self.parse, meta={'new_url': new_url})
                
                def prepare_files_for_append(self):
                    """Prepare CSV files for append mode"""
                    files_and_headers = [
                        (detail_hotel_file, ['hotel_id', 'hotel_name', 'hotel_address', 'number_of_room', 'url', 'rate', 'area', 'last_crawled']),
                        (hotel_rooms_file, ['hotel_id', 'room_name', 'roombox_size']),
                        (hotel_plans_file, ['hotel_id', 'plan_title', 'plan_term', 'plan_description']),
                        (hotel_plans_cotent_file, ['hotel_id', 'plan_room_type', 'plan_meal', 'plan_buget'])
                    ]
                    
                    for file_path, headers in files_and_headers:
                        if not os.path.exists(file_path):
                            with open(file_path, 'w', newline='', encoding=UTF_8_ENCODING) as csvfile:
                                writer = csv.DictWriter(csvfile, fieldnames=headers)
                                writer.writeheader()
            
            # Run the crawler
            process = CrawlerProcess({
                'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'DOWNLOAD_DELAY': 1,
                'RANDOMIZE_DOWNLOAD_DELAY': True,
                'CONCURRENT_REQUESTS': 8,
                'CONCURRENT_REQUESTS_PER_DOMAIN': 4,
                'RETRY_TIMES': 3,
                'RETRY_HTTP_CODES': [500, 502, 503, 504, 408, 429],
                'LOG_LEVEL': 'INFO'
            })
            
            process.crawl(IncrementalSpider)
            process.start()
            
            print("✅ Incremental crawl completed!")
            return True
            
        except Exception as e:
            print(f"❌ Error running incremental crawler: {e}")
            return False

    def show_incremental_results(self, hotels_crawled):
        """Show results of incremental crawl"""
        print("\n" + "="*60)
        print("📊 INCREMENTAL CRAWL RESULTS:")
        print("="*60)
        print(f"🆕 New hotels crawled: {len(self.new_hotels):,}")
        print(f"🔄 Updated hotels: {len(self.updated_hotels):,}")
        print(f"📊 Total hotels processed: {hotels_crawled:,}")
        print(f"💾 Backup location: {self.backup_dir}")
        print("="*60)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Incremental Rakuten Hotel Crawler')
    parser.add_argument('--force-update-days', '-d', type=int, default=7,
                       help='Force update hotels older than X days (default: 7)')
    parser.add_argument('--backup', '-b', action='store_true',
                       help='Create backup before crawling')
    
    args = parser.parse_args()
    
    crawler = IncrementalCrawler()
    success = crawler.run_incremental_crawl(args.force_update_days)
    
    if success:
        print("🎉 Incremental crawl completed successfully!")
    else:
        print("❌ Incremental crawl failed!")
    
    return success

if __name__ == "__main__":
    main()
