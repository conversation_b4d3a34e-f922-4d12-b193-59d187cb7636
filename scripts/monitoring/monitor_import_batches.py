#!/usr/bin/env python3
"""
Script to monitor import batches and analyze timestamp data
"""

import pymysql
from sshtunnel import SSHTunnelForwarder
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

# Database configuration
SSH_HOST = os.getenv('SSH_HOST')
SSH_USERNAME = os.getenv('SSH_USERNAME')
SSH_PORT = 22
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_NAME')
SSH_KEY_NAME = os.getenv('SSH_KEY_NAME')
SSH_PKEY = os.path.join('src/rakuten', SSH_KEY_NAME)

def monitor_import_batches():
    """Monitor import batches and analyze timestamp data"""
    
    print('📊 MONITORING IMPORT BATCHES AND TIMESTAMP DATA')
    print('=' * 60)
    
    try:
        # Open SSH tunnel
        print('🔗 Opening SSH tunnel...')
        tunnel = SSHTunnelForwarder(
            (SSH_HOST, SSH_PORT),
            ssh_username=SSH_USERNAME,
            ssh_pkey=SSH_PKEY,
            remote_bind_address=(DB_HOST, 3306)
        )
        tunnel.start()
        print(f'   - SSH tunnel opened on port {tunnel.local_bind_port}')
        
        # Connect to database
        print('🗄️  Connecting to database...')
        connection = pymysql.connect(
            host='127.0.0.1',
            user=DB_USER,
            passwd=DB_PASSWORD,
            db=DB_NAME,
            port=tunnel.local_bind_port
        )
        print('   - Database connected successfully')
        
        cursor = connection.cursor()
        
        # Check import_batches table
        print('\n📋 IMPORT BATCHES OVERVIEW:')
        print('=' * 40)
        cursor.execute('''
            SELECT batch_id, import_type, status, start_time, end_time,
                   total_records, imported_records, skipped_records, error_records,
                   source_file
            FROM import_batches 
            ORDER BY start_time DESC
        ''')
        batches = cursor.fetchall()
        
        if batches:
            for batch in batches:
                batch_id, import_type, status, start_time, end_time, total_records, imported_records, skipped_records, error_records, source_file = batch
                
                print(f'\n📦 Batch: {batch_id}')
                print(f'   - Type: {import_type}')
                print(f'   - Status: {status}')
                print(f'   - Start: {start_time}')
                print(f'   - End: {end_time}')
                if end_time and start_time:
                    duration = end_time - start_time
                    print(f'   - Duration: {duration}')
                print(f'   - Total Records: {total_records:,}')
                print(f'   - Imported: {imported_records:,}')
                print(f'   - Skipped: {skipped_records:,}')
                print(f'   - Errors: {error_records:,}')
                print(f'   - Source: {source_file}')
                
                if total_records > 0:
                    success_rate = (imported_records / total_records) * 100
                    print(f'   - Success Rate: {success_rate:.1f}%')
        else:
            print('   - No import batches found')
        
        # Analyze hotel timestamps
        print('\n⏰ HOTEL TIMESTAMP ANALYSIS:')
        print('=' * 35)
        
        # Count hotels by import batch
        cursor.execute('''
            SELECT import_batch_id, COUNT(*) as count,
                   MIN(created_at) as first_created,
                   MAX(created_at) as last_created,
                   MIN(imported_at) as first_imported,
                   MAX(imported_at) as last_imported
            FROM hotels 
            WHERE import_batch_id IS NOT NULL
            GROUP BY import_batch_id
            ORDER BY first_imported DESC
        ''')
        batch_stats = cursor.fetchall()
        
        if batch_stats:
            for stats in batch_stats:
                batch_id, count, first_created, last_created, first_imported, last_imported = stats
                print(f'\n🏨 Batch: {batch_id}')
                print(f'   - Hotels: {count:,}')
                print(f'   - Created: {first_created} to {last_created}')
                print(f'   - Imported: {first_imported} to {last_imported}')
        
        # Show recent hotel activity
        print('\n🕒 RECENT HOTEL ACTIVITY (Last 24 hours):')
        print('=' * 45)
        
        yesterday = datetime.now() - timedelta(days=1)
        cursor.execute('''
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN created_at > %s THEN 1 END) as created_today,
                   COUNT(CASE WHEN updated_at > %s THEN 1 END) as updated_today,
                   COUNT(CASE WHEN imported_at > %s THEN 1 END) as imported_today
            FROM hotels
        ''', (yesterday, yesterday, yesterday))
        
        activity = cursor.fetchone()
        if activity:
            total, created_today, updated_today, imported_today = activity
            print(f'   - Total hotels: {total:,}')
            print(f'   - Created in last 24h: {created_today:,}')
            print(f'   - Updated in last 24h: {updated_today:,}')
            print(f'   - Imported in last 24h: {imported_today:,}')
        
        # Show data quality metrics
        print('\n📈 DATA QUALITY METRICS:')
        print('=' * 30)
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total_hotels,
                COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as has_name,
                COUNT(CASE WHEN address IS NOT NULL AND address != '' THEN 1 END) as has_address,
                COUNT(CASE WHEN number_of_room > 0 THEN 1 END) as has_rooms,
                COUNT(CASE WHEN rate IS NOT NULL AND rate != '' THEN 1 END) as has_rate,
                COUNT(CASE WHEN postal_code IS NOT NULL AND postal_code != '' THEN 1 END) as has_postal,
                COUNT(CASE WHEN area IS NOT NULL AND area != '' THEN 1 END) as has_area,
                COUNT(CASE WHEN is_verified = 1 THEN 1 END) as verified
            FROM hotels
        ''')
        
        quality = cursor.fetchone()
        if quality:
            total, has_name, has_address, has_rooms, has_rate, has_postal, has_area, verified = quality
            print(f'   - Total hotels: {total:,}')
            print(f'   - Has name: {has_name:,} ({(has_name/total*100):.1f}%)')
            print(f'   - Has address: {has_address:,} ({(has_address/total*100):.1f}%)')
            print(f'   - Has room count: {has_rooms:,} ({(has_rooms/total*100):.1f}%)')
            print(f'   - Has rating: {has_rate:,} ({(has_rate/total*100):.1f}%)')
            print(f'   - Has postal code: {has_postal:,} ({(has_postal/total*100):.1f}%)')
            print(f'   - Has area: {has_area:,} ({(has_area/total*100):.1f}%)')
            print(f'   - Verified: {verified:,} ({(verified/total*100):.1f}%)')
        
        # Show import source distribution
        print('\n📊 IMPORT SOURCE DISTRIBUTION:')
        print('=' * 35)
        
        cursor.execute('''
            SELECT import_source, COUNT(*) as count
            FROM hotels 
            WHERE import_source IS NOT NULL
            GROUP BY import_source
            ORDER BY count DESC
        ''')
        
        sources = cursor.fetchall()
        if sources:
            for source, count in sources:
                print(f'   - {source}: {count:,} hotels')
        
        # Show data version distribution
        print('\n🔢 DATA VERSION DISTRIBUTION:')
        print('=' * 35)
        
        cursor.execute('''
            SELECT data_version, COUNT(*) as count
            FROM hotels 
            WHERE data_version IS NOT NULL
            GROUP BY data_version
            ORDER BY data_version DESC
        ''')
        
        versions = cursor.fetchall()
        if versions:
            for version, count in versions:
                print(f'   - Version {version}: {count:,} hotels')
        
        # Show sample hotels with full timestamp info
        print('\n🏨 SAMPLE HOTELS WITH TIMESTAMP INFO:')
        print('=' * 45)
        
        cursor.execute('''
            SELECT name, area, in_site_id, created_at, updated_at, imported_at, 
                   import_batch_id, import_source, data_version
            FROM hotels 
            ORDER BY updated_at DESC 
            LIMIT 5
        ''')
        
        sample_hotels = cursor.fetchall()
        if sample_hotels:
            for hotel in sample_hotels:
                name, area, hotel_id, created_at, updated_at, imported_at, batch_id, source, version = hotel
                print(f'\n   🏨 {name[:40]}...')
                print(f'      - Area: {area[:30]}...')
                print(f'      - Hotel ID: {hotel_id}')
                print(f'      - Created: {created_at}')
                print(f'      - Updated: {updated_at}')
                print(f'      - Imported: {imported_at}')
                print(f'      - Batch: {batch_id}')
                print(f'      - Source: {source}')
                print(f'      - Version: {version}')
        
        cursor.close()
        connection.close()
        tunnel.close()
        
        print(f'\n✅ Batch monitoring completed!')
        
    except Exception as e:
        print(f'❌ Monitoring error: {e}')
        return None

if __name__ == "__main__":
    monitor_import_batches()
