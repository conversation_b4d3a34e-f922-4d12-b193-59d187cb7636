#!/usr/bin/env python3
"""
Daily monitoring dashboard for Rakuten Travel operations
"""

import os
import sys
import json
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pymysql
from sshtunnel import SSHTunnelForwarder
from dotenv import load_dotenv

load_dotenv()

# Database configuration
SSH_HOST = os.getenv('SSH_HOST')
SSH_USERNAME = os.getenv('SSH_USERNAME')
SSH_PORT = 22
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_NAME')
SSH_KEY_NAME = os.getenv('SSH_KEY_NAME')
SSH_PKEY = os.path.join('src/rakuten', SSH_KEY_NAME)

def get_database_connection():
    """Get database connection via SSH tunnel"""
    tunnel = SSHTunnelForwarder(
        (SSH_HOST, SSH_PORT),
        ssh_username=SSH_USERNAME,
        ssh_pkey=SSH_PKEY,
        remote_bind_address=(DB_HOST, 3306)
    )
    tunnel.start()
    
    connection = pymysql.connect(
        host='127.0.0.1',
        user=DB_USER,
        passwd=DB_PASSWORD,
        db=DB_NAME,
        port=tunnel.local_bind_port
    )
    
    return tunnel, connection

def generate_daily_dashboard():
    """Generate daily monitoring dashboard"""
    
    print('📊 RAKUTEN TRAVEL DAILY DASHBOARD')
    print('=' * 60)
    print(f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        tunnel, connection = get_database_connection()
        cursor = connection.cursor()
        
        # 1. Overall statistics
        print('\n📈 OVERALL STATISTICS:')
        print('-' * 30)
        
        cursor.execute('SELECT COUNT(*) FROM hotels')
        total_hotels = cursor.fetchone()[0]
        print(f'   Total Hotels: {total_hotels:,}')
        
        cursor.execute('SELECT COUNT(*) FROM areas')
        total_areas = cursor.fetchone()[0]
        print(f'   Total Areas: {total_areas:,}')
        
        cursor.execute('SELECT COUNT(*) FROM sub_areas')
        total_sub_areas = cursor.fetchone()[0]
        print(f'   Total Sub Areas: {total_sub_areas:,}')
        
        # 2. Recent import activity
        print('\n🔄 RECENT IMPORT ACTIVITY (Last 7 days):')
        print('-' * 45)
        
        seven_days_ago = datetime.now() - timedelta(days=7)
        cursor.execute('''
            SELECT batch_id, import_type, status, start_time, end_time,
                   total_records, imported_records, error_records
            FROM import_batches 
            WHERE start_time >= %s
            ORDER BY start_time DESC
        ''', (seven_days_ago,))
        
        recent_batches = cursor.fetchall()
        if recent_batches:
            for batch in recent_batches:
                batch_id, import_type, status, start_time, end_time, total_records, imported_records, error_records = batch
                duration = end_time - start_time if end_time else 'Running'
                print(f'   📦 {batch_id}')
                print(f'      Status: {status} | Records: {imported_records:,}/{total_records:,} | Errors: {error_records:,}')
                print(f'      Duration: {duration} | Started: {start_time}')
        else:
            print('   No recent import activity')
        
        # 3. Data quality metrics
        print('\n📊 DATA QUALITY METRICS:')
        print('-' * 30)
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as has_name,
                COUNT(CASE WHEN area IS NOT NULL AND area != '' THEN 1 END) as has_area,
                COUNT(CASE WHEN rate IS NOT NULL AND rate != '' THEN 1 END) as has_rate
            FROM hotels
        ''')
        
        quality = cursor.fetchone()
        total, has_name, has_area, has_rate = quality
        
        print(f'   Data Completeness:')
        print(f'     - Name: {has_name:,}/{total:,} ({(has_name/total*100):.1f}%)')
        print(f'     - Area: {has_area:,}/{total:,} ({(has_area/total*100):.1f}%)')
        print(f'     - Rating: {has_rate:,}/{total:,} ({(has_rate/total*100):.1f}%)')
        
        # 4. Top areas by hotel count
        print('\n🏆 TOP 10 AREAS BY HOTEL COUNT:')
        print('-' * 35)
        
        cursor.execute('''
            SELECT area, COUNT(*) as count
            FROM hotels 
            GROUP BY area 
            ORDER BY count DESC 
            LIMIT 10
        ''')
        
        top_areas = cursor.fetchall()
        for i, (area, count) in enumerate(top_areas, 1):
            print(f'   {i:2d}. {area[:40]}... ({count:,} hotels)')
        
        # 5. Recent hotel updates
        print('\n🕒 RECENT HOTEL UPDATES (Last 24 hours):')
        print('-' * 45)
        
        yesterday = datetime.now() - timedelta(days=1)
        cursor.execute('''
            SELECT COUNT(*) as updated_count
            FROM hotels 
            WHERE updated_at >= %s
        ''', (yesterday,))
        
        updated_count = cursor.fetchone()[0]
        print(f'   Hotels updated in last 24h: {updated_count:,}')
        
        # 6. System health check
        print('\n🏥 SYSTEM HEALTH CHECK:')
        print('-' * 25)
        
        # Check for failed imports
        cursor.execute('''
            SELECT COUNT(*) 
            FROM import_batches 
            WHERE status = 'failed' AND start_time >= %s
        ''', (seven_days_ago,))
        
        failed_imports = cursor.fetchone()[0]
        
        # Check for stale data
        three_days_ago = datetime.now() - timedelta(days=3)
        cursor.execute('''
            SELECT COUNT(*) 
            FROM hotels 
            WHERE updated_at < %s
        ''', (three_days_ago,))
        
        stale_hotels = cursor.fetchone()[0]
        
        print(f'   Failed imports (7 days): {failed_imports}')
        print(f'   Stale hotels (>3 days): {stale_hotels:,}')
        
        # Health status
        if failed_imports == 0 and stale_hotels < total * 0.1:
            print('   Status: ✅ HEALTHY')
        elif failed_imports > 0 or stale_hotels > total * 0.5:
            print('   Status: ❌ CRITICAL')
        else:
            print('   Status: ⚠️ WARNING')
        
        # 7. File system check
        print('\n💾 FILE SYSTEM STATUS:')
        print('-' * 25)
        
        data_dir = 'data/raw/rakuten'
        if os.path.exists(data_dir):
            files = os.listdir(data_dir)
            csv_files = [f for f in files if f.endswith('.csv')]
            print(f'   Data directory: {data_dir}')
            print(f'   CSV files: {len(csv_files)}')
            
            # Check file sizes
            for file in csv_files:
                file_path = os.path.join(data_dir, file)
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    modified = datetime.fromtimestamp(os.path.getmtime(file_path))
                    print(f'     - {file}: {size:,} bytes (Modified: {modified.strftime("%Y-%m-%d %H:%M")})')
        else:
            print('   ❌ Data directory not found')
        
        # 8. Recommendations
        print('\n💡 RECOMMENDATIONS:')
        print('-' * 20)
        
        if failed_imports > 0:
            print('   ⚠️ Check failed import logs')
        
        if stale_hotels > total * 0.1:
            print('   ⚠️ Consider running full refresh')
        
        if updated_count == 0:
            print('   ⚠️ No recent updates - check crawler')
        
        if failed_imports == 0 and stale_hotels < total * 0.05 and updated_count > 0:
            print('   ✅ System running optimally')
        
        cursor.close()
        connection.close()
        tunnel.close()
        
        print(f'\n✅ Dashboard generated successfully!')
        
    except Exception as e:
        print(f'❌ Dashboard generation error: {e}')

if __name__ == "__main__":
    generate_daily_dashboard()
