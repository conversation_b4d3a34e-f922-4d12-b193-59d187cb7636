import scrapy

main_area_csv_file = './data/raw/rakuten/main_areas_file.csv'
list_subarea_file = './data/raw/rakuten/list_subarea_file.csv'
list_subarea_detail_file = './data/raw/rakuten/list_subarea_detail_file.csv'
list_hotel_file = './data/raw/rakuten/list_subarea_detail_file.csv'
list_hotel_detail_url_file = './data/raw/rakuten/list_hotel_detail_url_file.csv'
detail_hotel_file = './data/raw/rakuten/detail_hotel_file.csv'
detail_hotel_file_1 = './data/raw/rakuten/detail_hotel_file_1.csv'
hotel_rooms_file = './data/raw/rakuten/hotel_rooms_file.csv'
hotel_plans_file = './data/raw/rakuten/hotel_plans_file.csv'
sale_hotel_file = './data/raw/rakuten/sale_hotel_file.csv'
arae_jalan_csv_file = './data/raw/jalan/main_file.csv'
list_jalan_area_file = './data/raw/jalan/list_subarea_file.csv'
sale_hotel_file = './data/raw/rakuten/sale_hotel_file.csv'
list_url_detail_hotel_jalan_file = './data/raw/jalan/list_url_detail_hotel_jalan_file.csv'
hotel_plans_cotent_file = './data/raw/rakuten/hotel_plans_cotent_file.csv'
hotel_comments_file = './data/raw/rakuten/hotel_comments.csv'
hotel_comment_details_file = './data/raw/rakuten/hotel_comment_details.csv'
UTF_8_ENCODING = 'utf-8'


class JapanAreasBase(scrapy.Item):
    url = scrapy.Field()


class PageAnalysis():
    def extract_current_page(link):
        p_index = link.rfind('p')
        if p_index != -1:
            page_substring = link[p_index + 1:]
            page_parts = page_substring.split('-')
            if page_parts and page_parts[0].isdigit():
                return int(page_parts[0])
        return 1

    def is_last_page(response):
        # Check if pagination exists
        paging_elements = response.css('li.pagingTo')
        if not paging_elements:
            # No pagination found, assume it's the last (and only) page
            return True

        number_pages = len(paging_elements[0].css('a')) + 1
        current_page = PageAnalysis.extract_current_page(response.request.url)
        return current_page == number_pages
