import json
import pandas as pd

def save_data_to_file(data, filename, file_format='json'):
    """
    Save data to a file in the specified format (JSON or CSV).

    Parameters:
    data (list or dict): The data to be saved.
    filename (str): The name of the file to save the data.
    file_format (str): The format to save the data (default is 'json').
                       Options: 'json' or 'csv'
    """
    print(f"Saving data to {filename}")
    if file_format == 'json':
        with open(filename, 'w') as file:
            json.dump(data, file, indent=4)
    elif file_format == 'csv':
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False)
    else:
        raise ValueError("Unsupported file format. Use 'json' or 'csv'.")

def format_url(url):
    if url.startswith('//'):
        return 'https:' + url
    elif not url.startswith('http://') and not url.startswith('https://'):
        return 'https://' + url
    return url

def format_url_jalan(url):
    return '//www.jalan.net/ikisaki/map/' + url + '?ccnt=2667-X-mapKen'

def format_full_url(url):
    if not url.startswith('http'):
        url = 'https://' + url
    return url.replace('https://', 'https://', 1).replace('////', '//')
