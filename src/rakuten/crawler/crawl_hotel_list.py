import sys
sys.path.append('./src')

import pandas as pd
from utils import format_url
from common import JapanAreasBase, PageAnalysis, list_hotel_detail_url_file, \
    list_hotel_file, UTF_8_ENCODING
from scrapy.crawler import CrawlerProcess
import scrapy


class JapanHotelInfo(JapanAreasBase):
    pass
    area = scrapy.Field()
    title_url_hotel = scrapy.Field()
    url = scrapy.Field()
    url_list = scrapy.Field()


class ListHotelWithDetailUrl(scrapy.Spider):
    name = 'list_hotel_spider'

    def start_requests(self):
        df = pd.read_csv(list_hotel_file, encoding=UTF_8_ENCODING)

        df['url'] = df['url'].apply(format_url)
        for _, row in df.iterrows():
            title = row['sub_area_2']
            url = row['url']
            yield scrapy.Request(url=url, meta={'title': title}, callback=self.parse)

    def parse(self, response):
        # Updated selector for new HTML structure
        li_maps = response.css('li')
        url_list = response.url

        for li_map in li_maps:
            # Look for hotel links in the new structure
            hotel_link = li_map.css('a[href*="/HOTEL/"]')
            if hotel_link:
                title_url_hotel = hotel_link.css('::text').get()
                url = hotel_link.css('::attr(href)').get()

                # Extract area from URL using improved method
                area = self.extract_area_from_url(url_list)

                if title_url_hotel and url:
                    # Ensure URL is properly formatted
                    if not url.startswith('http'):
                        url = 'https://travel.rakuten.co.jp' + url
                    yield JapanHotelInfo(title_url_hotel=title_url_hotel.strip(), url=url, area=area, url_list=url_list)

        # Handle pagination
        if not PageAnalysis.is_last_page(response):
            next_page = response.css('li.pagingBack a')[0]
            yield response.follow(next_page, self.parse)

    def extract_area_from_url(self, url):
        """Extract area name from Rakuten travel URL"""
        import re
        try:
            # Pattern for extracting prefecture/area from URL
            match = re.search(r'/yado/([^/]+)/', url)
            if match:
                area = match.group(1)
                # Map common prefecture codes to names
                area_mapping = {
                    'tokyo': '東京', 'osaka': '大阪', 'kyoto': '京都', 'aichi': '愛知',
                    'kanagawa': '神奈川', 'hokkaido': '北海道', 'fukuoka': '福岡', 'okinawa': '沖縄',
                    'hiroshima': '広島', 'miyagi': '宮城', 'nagano': '長野', 'shizuoka': '静岡',
                    'chiba': '千葉', 'saitama': '埼玉', 'hyogo': '兵庫', 'ibaraki': '茨城',
                    'tochigi': '栃木', 'gunma': '群馬', 'niigata': '新潟', 'toyama': '富山',
                    'ishikawa': '石川', 'fukui': '福井', 'yamanashi': '山梨', 'gifu': '岐阜',
                    'mie': '三重', 'shiga': '滋賀', 'nara': '奈良', 'wakayama': '和歌山',
                    'tottori': '鳥取', 'shimane': '島根', 'okayama': '岡山', 'yamaguchi': '山口',
                    'tokushima': '徳島', 'kagawa': '香川', 'ehime': '愛媛', 'kochi': '高知',
                    'saga': '佐賀', 'nagasaki': '長崎', 'kumamoto': '熊本', 'oita': '大分',
                    'miyazaki': '宮崎', 'kagoshima': '鹿児島', 'fukushima': '福島',
                    'yamagata': '山形', 'iwate': '岩手', 'aomori': '青森', 'akita': '秋田'
                }
                return area_mapping.get(area, area)
            return 'Unknown'
        except:
            return 'Unknown'


def create_crawler_process():
    settings = {
        'FEEDS': {
            list_hotel_detail_url_file: {
                'format': 'csv',
                'overwrite': True
            }
        },
        'LOG_LEVEL': 'INFO'
    }
    return CrawlerProcess(settings=settings)


if __name__ == "__main__":
    process = create_crawler_process()
    process.crawl(ListHotelWithDetailUrl)
    process.start()
    print('Data extraction and update completed.')
