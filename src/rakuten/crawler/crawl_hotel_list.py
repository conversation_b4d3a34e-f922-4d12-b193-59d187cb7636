import sys
sys.path.append('./src')

import pandas as pd
from utils import format_url
from common import JapanAreasBase, PageAnalysis, list_hotel_detail_url_file, \
    list_hotel_file, UTF_8_ENCODING
from scrapy.crawler import CrawlerProcess
import scrapy


class JapanHotelInfo(JapanAreasBase):
    pass
    area = scrapy.Field()
    title_url_hotel = scrapy.Field()
    url = scrapy.Field()
    url_list = scrapy.Field()


class ListHotelWithDetailUrl(scrapy.Spider):
    name = 'list_hotel_spider'

    def start_requests(self):
        df = pd.read_csv(list_hotel_file, encoding=UTF_8_ENCODING,
                         header=None, names=['sub_area_2', 'url'],
                         skiprows=1)

        df['url'] = df['url'].apply(format_url)
        for title, url in df[['sub_area_2', 'url']].itertuples(index=False):
            yield scrapy.Request(url=url, meta={'title': title}, callback=self.parse)

    def parse(self, response):
        li_maps = response.css('.htlHead .info')
        url_list = response.url

        for li_map in li_maps:
            area = li_map.css('.area ::text').get()
            title_url_hotel = li_map.css('h1 > a ::text').get()
            url = li_map.css('h1 > a ::attr(href)').get()

            if title_url_hotel and url:
                yield JapanHotelInfo(title_url_hotel=title_url_hotel.strip(), url=url, area=area.strip(), url_list=url_list)

        if not PageAnalysis.is_last_page(response):
            next_page = response.css('li.pagingBack a')[0]
            yield response.follow(next_page, self.parse)


def create_crawler_process():
    settings = {
        'FEEDS': {
            list_hotel_detail_url_file: {
                'format': 'csv',
                'overwrite': True
            }
        }
    }
    return CrawlerProcess(settings=settings)


if __name__ == "__main__":
    process = create_crawler_process()
    process.crawl(ListHotelWithDetailUrl)
    process.start()
    print('Data extraction and update completed.')
