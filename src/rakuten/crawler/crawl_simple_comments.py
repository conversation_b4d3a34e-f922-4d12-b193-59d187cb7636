#!/usr/bin/env python3
"""
Simplified Rakuten Hotel Comments Crawler
Only crawl essential comment information: rating, reviewer name, date, and comment text
"""

import scrapy
import re
import csv
import os
from datetime import datetime
from urllib.parse import urljoin
import pandas as pd

# Add project root to path
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.common import UTF_8_ENCODING

class SimpleHotelCommentsSpider(scrapy.Spider):
    name = 'simple_hotel_comments'
    allowed_domains = ['travel.rakuten.co.jp']
    
    # Output file
    comments_file = './data/raw/rakuten/simple_hotel_comments.csv'
    
    def __init__(self, hotel_id=None, *args, **kwargs):
        super(SimpleHotelCommentsSpider, self).__init__(*args, **kwargs)
        self.hotel_id = hotel_id
        self.comments_count = 0
        self.pages_crawled = 0
        
        # Initialize CSV file
        self.init_csv_file()
    
    def init_csv_file(self):
        """Initialize CSV file with headers"""
        with open(self.comments_file, 'w', newline='', encoding=UTF_8_ENCODING) as csvfile:
            fieldnames = [
                'hotel_id', 'reviewer_name', 'overall_rating', 
                'comment_text', 'comment_date', 'crawled_at'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
    
    def start_requests(self):
        """Generate initial requests"""
        if self.hotel_id:
            # Single hotel mode
            url = f"https://travel.rakuten.co.jp/HOTEL/{self.hotel_id}/review.html"
            yield scrapy.Request(url=url, callback=self.parse_comments_page, 
                               meta={'hotel_id': self.hotel_id, 'page': 1})
        else:
            # Multiple hotels mode - read from hotel list
            hotel_list_file = './data/raw/rakuten/detail_hotel_file.csv'
            if os.path.exists(hotel_list_file):
                df = pd.read_csv(hotel_list_file, encoding=UTF_8_ENCODING)
                for _, row in df.iterrows():
                    hotel_id = str(row['hotel_id'])
                    url = f"https://travel.rakuten.co.jp/HOTEL/{hotel_id}/review.html"
                    yield scrapy.Request(url=url, callback=self.parse_comments_page,
                                       meta={'hotel_id': hotel_id, 'page': 1})
    
    def parse_comments_page(self, response):
        """Parse comments page and handle pagination"""
        hotel_id = response.meta['hotel_id']
        page = response.meta['page']
        
        self.logger.info(f"Parsing comments for hotel {hotel_id}, page {page}")
        
        # Extract comments from current page
        comments = self.extract_comments(response, hotel_id)
        self.comments_count += len(comments)
        
        # Save comments directly to CSV
        for comment in comments:
            self.save_comment_to_csv(comment)
        
        self.logger.info(f"Extracted {len(comments)} comments from page {page}")
        
        # Check for next page
        next_page_url = self.get_next_page_url(response)
        if next_page_url:
            self.pages_crawled += 1
            yield scrapy.Request(
                url=next_page_url,
                callback=self.parse_comments_page,
                meta={'hotel_id': hotel_id, 'page': page + 1}
            )
        else:
            self.logger.info(f"Completed crawling hotel {hotel_id}: {self.comments_count} comments, {self.pages_crawled + 1} pages")
    
    def extract_comments(self, response, hotel_id):
        """Extract all comments from current page"""
        comments = []

        # Look for comment boxes based on Rakuten's actual structure
        comment_boxes = response.css('div.commentBox')

        # If no comments found, try alternative selectors
        if not comment_boxes:
            comment_boxes = response.css('div.comment, li.commentItem, div[class*="comment"]')

        for comment_box in comment_boxes:
            comment_data = self.extract_single_comment(comment_box, hotel_id)
            if comment_data:
                comments.append(comment_data)

        return comments
    
    def extract_single_comment(self, comment_box, hotel_id):
        """Extract essential comment data from a comment box"""
        try:
            # Extract reviewer name - based on actual HTML structure
            reviewer_name = ""

            # Try h1.commentTitle first
            name_element = comment_box.css('h1.commentTitle::text').get()
            if name_element:
                name_match = re.search(r'^(.+?)さんの', name_element.strip())
                if name_match:
                    reviewer_name = name_match.group(1) + "さん"

            # If not found, try span.user
            if not reviewer_name:
                user_element = comment_box.css('span.user::text').get()
                if user_element:
                    name_match = re.search(r'^(.+?)さん', user_element.strip())
                    if name_match:
                        reviewer_name = name_match.group(1) + "さん"
            
            # Extract overall rating - based on actual HTML structure
            overall_rating = ""

            # Look for rating in p.commentRate span.rate
            rate_element = comment_box.css('p.commentRate span.rate').get()
            if rate_element:
                # Extract rating value from text
                rate_text = comment_box.css('p.commentRate span.rate::text').get()
                if rate_text:
                    rate_text = rate_text.strip()
                    # Check if it's a low rating (1 or 2)
                    if rate_text in ['1', '2']:
                        overall_rating = f"最低 {rate_text}"
                    else:
                        overall_rating = rate_text
            
            # Extract comment text - based on actual HTML structure
            comment_text = ""

            # Look for comment text in p.commentSentence - get ALL text including <br/> separated content
            text_element = comment_box.css('p.commentSentence')
            if text_element:
                # Get all text content, including text separated by <br/> tags
                all_texts = text_element.css('::text').getall()
                if all_texts:
                    # Join all text parts and clean up
                    comment_text = ' '.join([t.strip() for t in all_texts if t.strip()])
                    # Remove extra whitespace
                    comment_text = ' '.join(comment_text.split())
            
            # Extract comment date - based on actual HTML structure
            comment_date = ""

            # Look for date in span.time
            date_element = comment_box.css('span.time::text').get()
            if date_element:
                comment_date = date_element.strip()
            
            # Skip if essential data is missing
            if not reviewer_name and not comment_text:
                return None
            
            # Create comment data
            comment_data = {
                'hotel_id': hotel_id,
                'reviewer_name': reviewer_name,
                'overall_rating': overall_rating,
                'comment_text': comment_text,
                'comment_date': comment_date,
                'crawled_at': datetime.now().isoformat()
            }
            
            return comment_data
            
        except Exception as e:
            self.logger.error(f"Error extracting comment: {e}")
            return None
    
    def get_next_page_url(self, response):
        """Get URL for next page of comments"""
        # Try multiple selectors for next page link
        next_selectors = [
            'li.pagingNext a::attr(href)',
            '.pagination .next::attr(href)',
            'a.next-page::attr(href)'
        ]
        
        for selector in next_selectors:
            next_link = response.css(selector).get()
            if next_link:
                return urljoin(response.url, next_link)
        
        return None
    
    def save_comment_to_csv(self, comment):
        """Save single comment to CSV file"""
        if not comment:
            return
        
        fieldnames = [
            'hotel_id', 'reviewer_name', 'overall_rating',
            'comment_text', 'comment_date', 'crawled_at'
        ]
        
        # Filter comment data to only include expected fields
        filtered_comment = {key: comment.get(key, '') for key in fieldnames}
        
        # Save to CSV file
        with open(self.comments_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writerow(filtered_comment)


if __name__ == "__main__":
    from scrapy.crawler import CrawlerProcess
    
    # Create crawler process
    process = CrawlerProcess({
        'LOG_LEVEL': 'INFO',
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Run spider
    process.crawl(SimpleHotelCommentsSpider)
    process.start()
