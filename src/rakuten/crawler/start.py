import sys
import os

script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(script_dir, '..', '..'))

from common import JapanAreasBase, main_area_csv_file
from config import RAKUTEN_URL
from utils import format_full_url
from scrapy.crawler import CrawlerProcess
import scrapy

class JapanAreas(JapanAreasBase):
    pass
    title = scrapy.Field()


class MainSpider(scrapy.Spider):
    name = 'main_area_spider'
    start_urls = [RAKUTEN_URL]

    def parse(self, response):
        li_maps = response.css('#mapDms > .mapList > li:not(:has(ul)) > a')
        for li_map in li_maps:
            title = li_map.css('::text').get()
            url = li_map.css('::attr(href)').get()
            if title and url and not url.startswith('javascript:void(0)'):
                full_url = format_full_url(url.strip())
                yield JapanAreas(title=title.strip(), url=full_url)


def handle_output(output):
    spider_stats = output.get('stats')
    scraped_items = spider_stats.get('item_scraped_count')
    print(f"Scraped {scraped_items} items")


def create_crawler_process():
    settings = {
        'FEEDS': {
            main_area_csv_file: {
                'format': 'csv',
                'overwrite': True
            }
        }
    }
    return CrawlerProcess(settings=settings)


if __name__ == "__main__":
    process = create_crawler_process()
    process.crawl(MainSpider, output_callback=handle_output)
    process.start()
    print('Spider finished running')
