import sys
sys.path.append('./src')

import pandas as pd
from utils import format_url
from common import JapanAreasBase, list_subarea_file, UTF_8_ENCODING, list_subarea_detail_file
from scrapy.crawler import CrawlerProcess
import scrapy


class JapanSubSubAreas(JapanAreasBase):
    pass
    area = scrapy.Field()
    sub_area = scrapy.Field()
    sub_area_2 = scrapy.Field()


class SubAreaListSpider(scrapy.Spider):
    name = 'sub_subarea_spider'

    def start_requests(self):
        df = pd.read_csv(list_subarea_file, encoding=UTF_8_ENCODING,
                         header=None, names=['area', 'sub_area', 'url'],
                         skiprows=1)

        df['url'] = df['url'].apply(format_url)
        for url, area, sub_area in df[['url', 'area', 'sub_area']].itertuples(index=False):
            yield scrapy.Request(url=url, meta={'sub_area': sub_area, 'area': area, 'url': url}, callback=self.parse)

    def parse(self, response):
        url = response.meta.get('url')
        sub_area = response.meta.get('sub_area')
        area = response.meta.get('area')
        if 'map' in url:
            for li_map in response.css('.borderBox > ul > li > a'):
                sub_area_2 = li_map.css('::text').get().strip()
                url = li_map.css('::attr(href)').get()
                if sub_area:
                    yield JapanSubSubAreas(sub_area=sub_area, url=url, area=area, sub_area_2=sub_area_2)
        else:
            url_hotel = f"https://{url.replace('https://', '')}"
            yield JapanSubSubAreas(sub_area=sub_area, url=url_hotel, area=area, sub_area_2=[])


def create_crawler_process():
    settings = {
        'FEEDS': {
            list_subarea_detail_file:  {
                'format': 'csv',
                'overwrite': True
            }
        }
    }
    return CrawlerProcess(settings=settings)


if __name__ == "__main__":
    process = create_crawler_process()
    process.crawl(SubAreaListSpider)
    process.start()
    print('Data extraction and update completed.')
