import sys
sys.path.append('./src')

import pandas as pd
from utils import format_url
from common import JapanAreasBase, main_area_csv_file, list_subarea_file, UTF_8_ENCODING
from scrapy.crawler import CrawlerProcess
import scrapy


class JapanSubAreas(JapanAreasBase):
    pass
    area = scrapy.Field()
    sub_area = scrapy.Field()


class AreaListSpider(scrapy.Spider):
    name = 'sub_area_spider'

    def start_requests(self):
        df = pd.read_csv(main_area_csv_file, encoding=UTF_8_ENCODING,
                         header=None, names=['title', 'url'],
                         skiprows=1)

        df['url'] = df['url'].apply(format_url)
        for title, url in df[['title', 'url']].itertuples(index=False):
            yield scrapy.Request(url=url, meta={'title': title}, callback=self.parse)

    def parse(self, response):
        area = response.meta.get('title').strip()
        for li_map in response.css('.areaList > li > a'):
            sub_area = li_map.css('::text').get().strip()
            partial_url = li_map.css('::attr(href)').get().strip()

            full_url = f"https://travel.rakuten.co.jp{partial_url}"
            if sub_area:
                yield JapanSubAreas(sub_area=sub_area, url=full_url, area=area)



def create_crawler_process():
    settings = {
        'FEEDS': {
            list_subarea_file: {
                'format': 'csv',
                'overwrite': True
            }
        }
    }
    return CrawlerProcess(settings=settings)


if __name__ == "__main__":
    process = create_crawler_process()
    process.crawl(AreaListSpider)
    process.start()
    print('Data extraction and update completed.')
