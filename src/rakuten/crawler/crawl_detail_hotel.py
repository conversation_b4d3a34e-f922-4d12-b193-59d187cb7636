import sys
sys.path.append('./src')

import scrapy
from scrapy.crawler import CrawlerProcess
from common import JapanAreasBase, list_hotel_detail_url_file, detail_hotel_file,\
    hotel_rooms_file, hotel_plans_file, hotel_plans_cotent_file, UTF_8_ENCODING
from utils import format_full_url
import pandas as pd
import csv
from datetime import datetime


class JapanDetailHotel(JapanAreasBase):
    pass
    title_url_hotel = scrapy.Field()
    hotel_id = scrapy.Field()
    hotel_name = scrapy.Field()
    hotel_address = scrapy.Field()
    number_of_room = scrapy.Field()
    rate = scrapy.Field()


class JapanRoomData(JapanAreasBase):
    pass
    hotel_id = scrapy.Field()
    room_name = scrapy.Field()
    roombox_size = scrapy.Field()


class JapanPlanData(JapanAreasBase):
    pass
    hotel_id = scrapy.Field()
    plan_title = scrapy.Field()
    plan_term = scrapy.Field()
    plan_description = scrapy.Field()


class JapanPlanContentData(JapanAreasBase):
    pass
    hotel_id = scrapy.Field()
    plan_buget = scrapy.Field()
    plan_room_type = scrapy.Field()
    plan_meal = scrapy.Field()


class DetailHotelSpider(scrapy.Spider):
    name = 'detail_hotel_spider'

    def extract_area_from_url(self, url):
        """Extract area from URL"""
        try:
            # Extract area from URL pattern
            if '/yado/' in url:
                area_part = url.split('/yado/')[1].split('/')[0]
                # Map English area names to Japanese
                area_mapping = {
                    'hokkaido': '北海道', 'aomori': '青森', 'iwate': '岩手', 'miyagi': '宮城',
                    'akita': '秋田', 'yamagata': '山形', 'fukushima': '福島', 'ibaraki': '茨城',
                    'tochigi': '栃木', 'gunma': '群馬', 'saitama': '埼玉', 'chiba': '千葉',
                    'tokyo': '東京', 'kanagawa': '神奈川', 'niigata': '新潟', 'toyama': '富山',
                    'ishikawa': '石川', 'fukui': '福井', 'yamanashi': '山梨', 'nagano': '長野',
                    'gifu': '岐阜', 'shizuoka': '静岡', 'aichi': '愛知', 'mie': '三重',
                    'shiga': '滋賀', 'kyoto': '京都', 'osaka': '大阪', 'hyogo': '兵庫',
                    'nara': '奈良', 'wakayama': '和歌山', 'tottori': '鳥取', 'shimane': '島根',
                    'okayama': '岡山', 'hiroshima': '広島', 'yamaguchi': '山口', 'tokushima': '徳島',
                    'kagawa': '香川', 'ehime': '愛媛', 'kochi': '高知', 'fukuoka': '福岡',
                    'saga': '佐賀', 'nagasaki': '長崎', 'kumamoto': '熊本', 'oita': '大分',
                    'miyazaki': '宮崎', 'kagoshima': '鹿児島', 'okinawa': '沖縄'
                }
                return area_mapping.get(area_part, area_part) + '県'
            return 'Unknown'
        except:
            return 'Unknown'

    def start_requests(self):
        df = pd.read_csv('./data/raw/rakuten/list_hotel_detail_url_file_unique.csv', encoding=UTF_8_ENCODING)

        with open(detail_hotel_file, 'w', newline='', encoding=UTF_8_ENCODING) as csvfile:
            fieldnames = ['hotel_id', 'hotel_name', 'hotel_address', 'number_of_room', 'url', 'rate', 'area', 'last_crawled']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

        for _, row in df.iterrows():
            url = row['url']
            new_url = f"{url.split('.html')[0]}_std.html"
            yield scrapy.Request(url=new_url, callback=self.parse, meta={'new_url': new_url})

    def parse(self, response):
        new_url = response.meta.get('new_url')
        hotel_id = response.url.split('/')[-2]

        # Extract hotel name
        hotel_name = None
        li_title_hotels = response.css('.not_prmHtl > a')
        if li_title_hotels:
            title = li_title_hotels[0].css('::text').get()
            if title:
                hotel_name = title.strip()

        # Extract rating - handle None case
        rate_element = response.css('#htlRnk > div > div > a > span.avrgNum > strong.rating::text').get()
        rate = rate_element.strip() if rate_element else None

        # Extract address and room count
        hotel_address = None
        number_of_room = None

        # Try to find address
        address_element = response.css('li[data-locate="hotel-address"] dl dd::text').get()
        if address_element:
            hotel_address = address_element.strip()

        # Try to find number of rooms
        room_element = response.css('li[data-locate="hotel-total-room"] dl dd::text').get()
        if room_element:
            number_of_room = room_element.strip()

        # Extract area from URL or use a default method
        area = self.extract_area_from_url(response.url)

        # Always write data, even if some fields are missing
        if hotel_name:  # Only require hotel name
            with open(detail_hotel_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                fieldnames = ['hotel_id', 'hotel_name', 'hotel_address', 'number_of_room', 'url', 'rate', 'area', 'last_crawled']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                if csvfile.tell() == 0:
                    writer.writeheader()

                writer.writerow({
                    'hotel_id': hotel_id,
                    'hotel_name': hotel_name,
                    'hotel_address': hotel_address,
                    'number_of_room': number_of_room,
                    'url': new_url,
                    'rate': rate,
                    'area': area,
                    'last_crawled': datetime.now().isoformat()
                })

            # Create and yield item
            item = JapanDetailHotel(
                hotel_name=hotel_name,
                hotel_id=hotel_id,
                hotel_address=hotel_address,
                number_of_room=number_of_room,
                rate=rate
            )
            yield item

        # Extract navigation links for rooms and plans
        nav_rooms = response.css('#navRoom > .rtconds')
        nav_plans = response.css('#navPlan > .rtconds')

        for nav_room in nav_rooms:
            url_room = nav_room.css('::attr(href)').get()
            url = format_full_url(url_room)
            if url_room:
                yield scrapy.Request(url=url, callback=self.parse_room_data, meta={'hotel_id': hotel_id})

        for nav_plan in nav_plans:
            url_plan = nav_plan.css('::attr(href)').get()
            url = format_full_url(url_plan)
            if url_plan:
                yield scrapy.Request(url=url, callback=self.parse_plan_data, meta={'hotel_id': hotel_id})

    def parse_room_data(self, response):
        roombox_lis = response.css('.roombox__li')
        room_data = None  # Initialize to avoid UnboundLocalError

        for roombox_li in roombox_lis:
            # Safe extraction with None handling
            room_name_element = roombox_li.css('.roombox__name ::text').get()
            room_name = room_name_element.strip() if room_name_element else "Unknown"

            roombox_size_element = roombox_li.css('.roombox__info .roombox__size-capacity ::text').get()
            roombox_size = roombox_size_element.strip() if roombox_size_element else "Unknown"

            room_data = JapanRoomData(room_name=room_name, roombox_size=roombox_size)

            with open(hotel_rooms_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                fieldnames = ['hotel_id', 'room_name', 'roombox_size']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                if csvfile.tell() == 0:
                    writer.writeheader()
                writer.writerow({
                    'hotel_id': response.meta.get('hotel_id'),
                    'room_name': room_data['room_name'],
                    'roombox_size': room_data['roombox_size'],
                })

            # Yield each room data item
            yield room_data

        # Handle pagination
        next_page = response.css('.plan-pagination a.plan-pagination__next-page::attr(href)').get()
        if next_page:
            yield response.follow(next_page, self.parse_room_data, meta=response.meta)

    def parse_plan_data(self, response):
        plan_lis = response.css('.htlPlnCsst .planThumb')
        plan_data = None  # Initialize to avoid UnboundLocalError

        for plan_li in plan_lis:
            # Safe extraction with None handling
            plan_title_element = plan_li.css('h4 ::text').get()
            plan_title = plan_title_element.strip() if plan_title_element else "Unknown"

            plan_term_element = plan_li.css('.htlPlnPrd ::text').get()
            plan_term = plan_term_element.strip() if plan_term_element else "Unknown"

            plan_description = plan_li.css('.htlPlnDtlPrv ::text').getall()
            plan_description = ''.join(plan_description).strip() if plan_description else "Unknown"

            plan_data = JapanPlanData(plan_title=plan_title, plan_term=plan_term, plan_description=plan_description)

            with open(hotel_plans_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                fieldnames = ['hotel_id', 'plan_title', 'plan_term', 'plan_description']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                if csvfile.tell() == 0:
                    writer.writeheader()
                writer.writerow({
                    'hotel_id': response.meta.get('hotel_id'),
                    'plan_title': plan_data['plan_title'],
                    'plan_term': plan_data['plan_term'],
                    'plan_description': plan_data['plan_description'],
                })

            # Yield plan data
            yield plan_data

            bugets = plan_li.css('.htlPlnRmTypLst .htlPlnRmTypPrc')
            plan_description_titles = plan_li.css('.htlPlnRmTypLst .htlPlnRmTypInfo')

            for buget, plan_description_title in zip(bugets, plan_description_titles):
                # Safe extraction with None handling
                plan_buget_element = buget.css('span > strong ::text').get()
                plan_buget = plan_buget_element.strip() if plan_buget_element else "Unknown"

                plan_room_type_element = plan_description_title.css('.htlPlnTypTxt > p ::text').get()
                plan_room_type = plan_room_type_element.strip() if plan_room_type_element else "Unknown"

                plan_meal = plan_description_title.css('.htlPlnTypTxt .htlPlnTypOpt > span[data-locate="roomType-option-meal"]::text').extract()
                if len(plan_meal) > 1:
                    plan_meal = ''.join(plan_meal[1:]).strip()
                elif len(plan_meal) == 1:
                    plan_meal = ''.join(plan_meal).strip()
                else:
                    plan_meal = "Unknown"

                plan_datas = JapanPlanContentData(plan_buget=plan_buget, plan_room_type=plan_room_type, plan_meal=plan_meal)

                with open(hotel_plans_cotent_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                    fieldnames = ['hotel_id', 'plan_room_type', 'plan_meal', 'plan_buget']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    if csvfile.tell() == 0:
                        writer.writeheader()
                    writer.writerow({
                        'hotel_id': response.meta.get('hotel_id'),
                        'plan_room_type': plan_datas['plan_room_type'],
                        'plan_meal': plan_datas['plan_meal'],
                        'plan_buget': plan_datas['plan_buget'],
                    })

                # Yield each plan content data
                yield plan_datas

        # Handle pagination
        next_page = response.css('.plan-pagination a.plan-pagination__next-page::attr(href)').get()
        if next_page:
            yield response.follow(next_page, self.parse_plan_data, meta=response.meta)

def handle_output(output):
    spider_stats = output.get('stats')
    scraped_items = spider_stats.get('item_scraped_count')
    print(f"Scraped {scraped_items} items")

if __name__ == "__main__":
    process = CrawlerProcess(settings={})
    process.crawl(DetailHotelSpider, output_callback=handle_output)
    process.start()
    print('Data extraction and update completed.')
