import sys
sys.path.append('./src')

import scrapy
from scrapy.crawler import CrawlerProcess
from common import JapanAreasBase, list_hotel_detail_url_file, detail_hotel_file,\
    hotel_rooms_file, hotel_plans_file, hotel_plans_cotent_file, UTF_8_ENCODING
from utils import format_full_url
import pandas as pd
import csv


class JapanDetailHotel(JapanAreasBase):
    pass
    title_url_hotel = scrapy.Field()
    hotel_id = scrapy.Field()
    hotel_name = scrapy.Field()
    hotel_address = scrapy.Field()
    number_of_room = scrapy.Field()
    rate = scrapy.Field()


class JapanRoomData(JapanAreasBase):
    pass
    hotel_id = scrapy.Field()
    room_name = scrapy.Field()
    roombox_size = scrapy.Field()


class JapanPlanData(JapanAreasBase):
    pass
    hotel_id = scrapy.Field()
    plan_title = scrapy.Field()
    plan_term = scrapy.Field()
    plan_description = scrapy.Field()


class JapanPlanContentData(JapanAreasBase):
    pass
    hotel_id = scrapy.Field()
    plan_buget = scrapy.Field()
    plan_room_type = scrapy.Field()
    plan_meal = scrapy.Field()


class DetailHotelSpider(scrapy.Spider):
    name = 'detail_hotel_spider'

    def start_requests(self):
        df = pd.read_csv(list_hotel_detail_url_file, encoding=UTF_8_ENCODING)

        with open(detail_hotel_file, 'w', newline='', encoding=UTF_8_ENCODING) as csvfile:
            fieldnames = ['hotel_id', 'hotel_name', 'hotel_address', 'number_of_room', 'url', 'rate']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

        for _, row in df.iterrows():
            url = row['url']
            new_url = f"{url.split('.html')[0]}_std.html"
            yield scrapy.Request(url=new_url, callback=self.parse, meta={'new_url': new_url})

    def parse(self, response):
        new_url = response.meta.get('new_url')
        hotel_id = response.url.split('/')[-2]
        li_title_hotels = response.css('.not_prmHtl > a')
        li_data_hotels = response.css('.dtlTbl')
        nav_rooms = response.css('#navRoom > .rtconds')
        nav_plans = response.css('#navPlan > .rtconds')
        rate = response.css('#htlRnk > div > div > a > span.avrgNum > strong.rating::text').get().strip()

        for li_title in li_title_hotels:
            title = li_title.css('::text').get()
            if title:
                item = JapanDetailHotel(hotel_name=title.strip(), hotel_id=hotel_id)

                for li_data in li_data_hotels:
                    address = li_data.css('li[data-locate="hotel-address"] dl dd::text').get()
                    number_of_room = li_data.css('li[data-locate="hotel-total-room"] dl dd::text').get()
                    if address:
                        item['number_of_room'] = number_of_room.strip()
                        item['hotel_address'] = address.strip()
                        with open(detail_hotel_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                            fieldnames = ['hotel_id', 'hotel_name', 'hotel_address', 'number_of_room', 'url', 'rate']
                            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                            if csvfile.tell() == 0:
                                writer.writeheader()

                            writer.writerow({
                                'hotel_id': item['hotel_id'],
                                'hotel_name': item['hotel_name'],
                                'hotel_address': item['hotel_address'],
                                'number_of_room': item['number_of_room'],
                                'url': new_url,
                                'rate': rate
                            })

                yield item

        for nav_room in nav_rooms:
            url_room = nav_room.css('::attr(href)').get()
            url = format_full_url(url_room)
            if url_room:
                yield scrapy.Request(url=url, callback=self.parse_room_data, meta={'hotel_id': hotel_id})

        for nav_plan in nav_plans:
            url_plan = nav_plan.css('::attr(href)').get()
            url = format_full_url(url_plan)
            if url_plan:
                yield scrapy.Request(url=url, callback=self.parse_plan_data, meta={'hotel_id': hotel_id})

    def parse_room_data(self, response):
        roombox_lis = response.css('.roombox__li')
        for roombox_li in roombox_lis:
            room_name = roombox_li.css('.roombox__name ::text').get().strip()
            roombox_size = roombox_li.css('.roombox__info .roombox__size-capacity ::text').get().strip()
            room_data = JapanRoomData(room_name=room_name, roombox_size=roombox_size)
            with open(hotel_rooms_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                fieldnames = ['hotel_id', 'room_name', 'roombox_size']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                if csvfile.tell() == 0:
                    writer.writeheader()
                writer.writerow({
                    'hotel_id': response.meta.get('hotel_id'),
                    'room_name': room_data['room_name'],
                    'roombox_size': room_data['roombox_size'],
                })

        yield room_data
        next_page = response.css('.plan-pagination a.plan-pagination__next-page::attr(href)').get()
        if next_page:
            yield response.follow(next_page, self.parse_room_data, meta=response.meta)

    def parse_plan_data(self, response):
        plan_lis = response.css('.htlPlnCsst .planThumb')

        for plan_li in plan_lis:
            plan_title = plan_li.css('h4 ::text').get().strip()
            plan_term = plan_li.css('.htlPlnPrd ::text').get().strip()
            plan_description = plan_li.css('.htlPlnDtlPrv ::text').getall()
            plan_description = ''.join(plan_description).strip()

            plan_data = JapanPlanData(plan_title=plan_title, plan_term=plan_term, plan_description=plan_description)
            with open(hotel_plans_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                fieldnames = ['hotel_id', 'plan_title', 'plan_term', 'plan_description']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                if csvfile.tell() == 0:
                    writer.writeheader()
                writer.writerow({
                    'hotel_id': response.meta.get('hotel_id'),
                    'plan_title': plan_data['plan_title'],
                    'plan_term': plan_data['plan_term'],
                    'plan_description': plan_data['plan_description'],
                })

            bugets = plan_li.css('.htlPlnRmTypLst .htlPlnRmTypPrc')
            plan_description_titles = plan_li.css('.htlPlnRmTypLst .htlPlnRmTypInfo')
            for buget, plan_description_title in zip(bugets, plan_description_titles):
                plan_buget = buget.css('span > strong ::text').get().strip()
                plan_room_type = plan_description_title.css('.htlPlnTypTxt > p ::text').get().strip()
                plan_meal = plan_description_title.css('.htlPlnTypTxt .htlPlnTypOpt > span[data-locate="roomType-option-meal"]::text').extract()
                if len(plan_meal) > 1:
                    plan_meal = ''.join(plan_meal[1:]).strip()
                else:
                    plan_meal = ''.join(plan_meal).strip()

                plan_datas = JapanPlanContentData(plan_buget=plan_buget, plan_room_type=plan_room_type, plan_meal=plan_meal)
                with open(hotel_plans_cotent_file, 'a', newline='', encoding=UTF_8_ENCODING) as csvfile:
                    fieldnames = ['hotel_id', 'plan_room_type', 'plan_meal', 'plan_buget']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    if csvfile.tell() == 0:
                        writer.writeheader()
                    writer.writerow({
                        'hotel_id': response.meta.get('hotel_id'),
                        'plan_room_type': plan_datas['plan_room_type'],
                        'plan_meal': plan_datas['plan_meal'],
                        'plan_buget': plan_datas['plan_buget'],
                    })
            yield plan_datas
        yield plan_data
        next_page = response.css('.plan-pagination a.plan-pagination__next-page::attr(href)').get()
        if next_page:
            yield response.follow(next_page, self.parse_plan_data, meta=response.meta)

def handle_output(output):
    spider_stats = output.get('stats')
    scraped_items = spider_stats.get('item_scraped_count')
    print(f"Scraped {scraped_items} items")

if __name__ == "__main__":
    process = CrawlerProcess(settings={})
    process.crawl(DetailHotelSpider, output_callback=handle_output)
    process.start()
    print('Data extraction and update completed.')
