import sys
sys.path.append('./src')
import scrapy
from scrapy.crawler import CrawlerProcess
from common import JapanAreasBase, arae_jalan_csv_file, list_jalan_area_file, UTF_8_ENCODING
from utils import format_url
import pandas as pd


class JapanSubAreas(JapanAreasBase):
    pass
    area = scrapy.Field()
    sub_area = scrapy.Field()


class AreaListSpider(scrapy.Spider):
    name = 'area_list_spider'

    def start_requests(self):
        df = pd.read_csv(arae_jalan_csv_file, encoding=UTF_8_ENCODING, header=None, names=['title', 'url'])
        df['url'] = df['url'].apply(format_url)
        for _, row in df.iterrows():
            url = row['url']
            title = row['title']
            yield scrapy.Request(url=url, meta={'title': title}, callback=self.parse)

    def parse(self, response):
        print(response)
        li_maps = response.css('.map-right-inner > #map-list01 > li > a')
        print(li_maps,'li_maps')
        for li_map in li_maps:
            area = response.meta.get('title')
            sub_area = li_map.css('::text').get()
            url = li_map.css('::attr(href)').get()
            if sub_area:
                item = JapanSubAreas(
                    sub_area=sub_area.strip(), url=url, area=area.strip())
                yield item


def handle_output(output):
    # Access the spider statistics using the 'get_stats()' method of the Crawler object
    spider_stats = output.get('stats')
    scraped_items = spider_stats.get('item_scraped_count')
    print(f"Scraped {scraped_items} items")


if __name__ == "__main__":
    # Create a CrawlerProcess and configure settings if needed
    process = CrawlerProcess(settings={
        # Add Scrapy settings here if required
        # 'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'FEEDS': {
            list_jalan_area_file: {
                'format': 'csv'
            }
        }
    })

    # Run the spider and scrape data
    process.crawl(AreaListSpider)
    process.start()

    print('Data extraction and update completed.')
