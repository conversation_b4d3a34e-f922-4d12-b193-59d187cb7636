import scrapy
import sys
sys.path.append('./src')
from scrapy.crawler import CrawlerProcess
from common import JapanAreasBase, list_url_detail_hotel_jalan_file, list_jalan_area_file, UTF_8_ENCODING
from utils import format_url
import pandas as pd
import re


class HotelDetailUrlJanlanAreas(JapanAreasBase):
    pass
    sub_area = scrapy.Field()


class ListHotelDetailUrlJanlanSpider(scrapy.Spider):
    name = 'list_hotel_detail_url_jalan_spider'

    def start_requests(self):
        df = pd.read_csv(list_jalan_area_file, encoding=UTF_8_ENCODING, header=None, names=['area', 'sub_area','url']) 
        df['url'] = df['url'].apply(format_url)
        for _, row in df.iterrows():
            url = row['url']
            sub_area= row['sub_area']
            yield scrapy.Request(url=url, meta={'sub_area': sub_area, 'url': url}, callback=self.parse)

    def parse(self, response):
        li_maps = response.css('.p-yadoCassette__body > a')
        url_page = response.meta.get('url')
        print(url_page,'url_page')
        for li_map in li_maps:
            sub_area = response.meta.get('sub_area')
            url = li_map.css('::attr(href)').get()
            if url and not url.startswith('javascript:'):
                if sub_area:
                    item = HotelDetailUrlJanlanAreas(
                        sub_area=sub_area.strip(), url=url)
                    yield item
        next_page = response.css('.pagerLink .next::attr(onclick)').get()
        if next_page:
            page_numbers = re.findall(r"selectPage\('30', '(\d+)'\)", next_page)
            if page_numbers:
                next_page_number = int(page_numbers[0])
                next_page_url = f'{url_page}page{next_page_number}.html'
                yield scrapy.Request(next_page_url, callback=self.parse, meta=response.meta)


def handle_output(output):
    # Access the spider statistics using the 'get_stats()' method of the Crawler object
    spider_stats = output.get('stats')
    scraped_items = spider_stats.get('item_scraped_count')
    print(f"Scraped {scraped_items} items")


if __name__ == "__main__":
    # Create a CrawlerProcess and configure settings if needed
    process = CrawlerProcess(settings={
        # Add Scrapy settings here if required
        # 'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'FEEDS': {
            list_url_detail_hotel_jalan_file: {
                'format': 'csv'
            }
        }
    })

    # Run the spider and scrape data
    process.crawl(ListHotelDetailUrlJanlanSpider)
    process.start()

    print('Data extraction and update completed.')
