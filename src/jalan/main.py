import scrapy
import sys
sys.path.append('./src')
from scrapy.crawler import CrawlerProcess
from config import JALAN_RUL
from common import JapanAreasBase ,arae_jalan_csv_file
from utils import format_url_jalan



class JapanAreas(JapanAreasBase):
    pass
    title = scrapy.Field()


class MainSpider(scrapy.Spider):
    name = 'main_spider'
    start_urls = [JALAN_RUL]

    def parse(self, response):
        noscript_content = response.css('noscript').get()
        import re
        href_lists = re.findall(r'href="(.*?)"', noscript_content)

        for href_list in href_lists:
            print(href_list.split('/map/')[-1])
            title = href_list.split('/map/')[-1]
            url = format_url_jalan(title)
            if title:
                item = JapanAreas(title=title.strip(),url=url.strip())
                yield item

# Define a callback function to handle the spider output
def handle_output(output):
    # Access the spider statistics using the 'get_stats()' method of the Crawler object
    spider_stats = output.get('stats')
    scraped_items = spider_stats.get('item_scraped_count')
    print(f"Scraped {scraped_items} items")


if __name__ == "__main__":
    # Create a CrawlerProcess and configure settings if needed
    process = CrawlerProcess(settings={
        # Add Scrapy settings here if required
        # 'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'FEEDS': {
            arae_jalan_csv_file: {
                'format': 'csv'
            }
        }
    })

    # Run the spider and scrape data
    process.crawl(MainSpider, output_callback=handle_output)
    process.start()
    print('Spider finished running')
