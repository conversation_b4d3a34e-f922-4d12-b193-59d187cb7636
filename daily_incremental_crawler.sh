#!/bin/bash
# Daily Incremental Crawler Script
# Auto-generated by setup_incremental_cron.sh

# Set environment
export PATH="/usr/local/bin:/usr/bin:/bin"
PROJECT_DIR="/var/www/micado-scraping"
LOG_DIR="$PROJECT_DIR/logs"
DATE=$(date +"%Y%m%d_%H%M%S")

# Change to project directory
cd "$PROJECT_DIR" || exit 1

# Log file for this run
LOG_FILE="$LOG_DIR/incremental_crawler_$DATE.log"

echo "🚀 Starting Incremental Crawler - $(date)" >> "$LOG_FILE"
echo "=================================================" >> "$LOG_FILE"

# Run incremental crawler
python3 run_incremental_crawler.py --force-update-days 7 >> "$LOG_FILE" 2>&1
CRAWLER_EXIT_CODE=$?

echo "=================================================" >> "$LOG_FILE"
echo "✅ Crawler completed with exit code: $CRAWLER_EXIT_CODE - $(date)" >> "$LOG_FILE"

# If crawler succeeded, run import to database
if [ $CRAWLER_EXIT_CODE -eq 0 ]; then
    echo "🔄 Starting database import..." >> "$LOG_FILE"
    python3 scripts/daily/enhanced_hotel_import.py --mode incremental >> "$LOG_FILE" 2>&1
    IMPORT_EXIT_CODE=$?
    echo "✅ Import completed with exit code: $IMPORT_EXIT_CODE - $(date)" >> "$LOG_FILE"
else
    echo "❌ Crawler failed, skipping database import" >> "$LOG_FILE"
fi

# Clean up old logs (keep last 30 days)
find "$LOG_DIR" -name "incremental_crawler_*.log" -mtime +30 -delete

# Create symlink to latest log
ln -sf "$LOG_FILE" "$LOG_DIR/latest_incremental.log"

echo "📊 Daily incremental crawler completed - $(date)" >> "$LOG_FILE"
