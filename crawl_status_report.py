#!/usr/bin/env python3
"""
Báo cáo tình trạng crawl dữ liệu Rakuten
"""

import pandas as pd
import os
from datetime import datetime
import glob

def generate_crawl_report():
    """Tạo báo cáo tình trạng crawl"""

    print("🎯 BÁO CÁO TÌNH TRẠNG CRAWL RAKUTEN")
    print("=" * 80)
    print(f"📅 Thời gian báo cáo: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 1. Kiểm tra các file dữ liệu chính
    print("📊 1. TÌNH TRẠNG CÁC FILE DỮ LIỆU CHÍNH")
    print("-" * 60)

    data_files = {
        'Main Areas': './data/raw/rakuten/main_areas_file.csv',
        'Sub Areas': './data/raw/rakuten/list_subarea_file.csv', 
        'Sub-Sub Areas': './data/raw/rakuten/list_subarea_detail_file.csv',
        'Hotel List': './data/raw/rakuten/list_hotel_detail_url_file.csv',
        'Hotel List (Unique)': './data/raw/rakuten/list_hotel_detail_url_file_unique.csv',
        'Hotel Details': './data/raw/rakuten/detail_hotel_file.csv',
        'Hotel Plans': './data/raw/rakuten/hotel_plans_file.csv',
        'Hotel Plans Content': './data/raw/rakuten/hotel_plans_cotent_file.csv',
        'Hotel Rooms': './data/raw/rakuten/hotel_rooms_file.csv',
        'Hotel Comments': './data/raw/rakuten/simple_hotel_comments.csv'
    }

    total_size = 0
    for name, file_path in data_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size

            # Đếm số dòng
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = sum(1 for _ in f)
                records = lines - 1 if lines > 0 else 0  # Trừ header

                print(f"✅ {name:<25}: {records:>8,} records ({size/1024/1024:.1f} MB)")
            except Exception as e:
                print(f"❌ {name:<25}: Error reading file - {str(e)}")
        else:
            print(f"❌ {name:<25}: File not found")

    print(f"\n📦 Tổng dung lượng dữ liệu: {total_size/1024/1024:.1f} MB")

    # 2. Phân tích dữ liệu hotel details
    print(f"\n📊 2. PHÂN TÍCH DỮ LIỆU HOTEL DETAILS")
    print("-" * 60)

    try:
        # Hotel list
        hotel_list_file = './data/raw/rakuten/list_hotel_detail_url_file.csv'
        if os.path.exists(hotel_list_file):
            df_hotels = pd.read_csv(hotel_list_file)
            print(f"🏨 Tổng số hotels trong list: {len(df_hotels):,}")

            # Unique hotels
            unique_file = './data/raw/rakuten/list_hotel_detail_url_file_unique.csv'
            if os.path.exists(unique_file):
                df_unique = pd.read_csv(unique_file)
                print(f"🏨 Số hotels unique: {len(df_unique):,}")
                print(f"🔄 Tỷ lệ duplicate: {((len(df_hotels) - len(df_unique))/len(df_hotels)*100):.1f}%")

        # Hotel details
        detail_file = './data/raw/rakuten/detail_hotel_file.csv'
        if os.path.exists(detail_file):
            df_details = pd.read_csv(detail_file)
            print(f"📋 Hotels có thông tin chi tiết: {len(df_details):,}")

        # Hotel plans
        plans_file = './data/raw/rakuten/hotel_plans_file.csv'
        if os.path.exists(plans_file):
            df_plans = pd.read_csv(plans_file)
            print(f"📋 Tổng số plans: {len(df_plans):,}")

        # Hotel rooms
        rooms_file = './data/raw/rakuten/hotel_rooms_file.csv'
        if os.path.exists(rooms_file):
            df_rooms = pd.read_csv(rooms_file)
            print(f"🏠 Tổng số rooms: {len(df_rooms):,}")

    except Exception as e:
        print(f"❌ Lỗi khi phân tích: {str(e)}")

    # 3. Kiểm tra comments
    print(f"\n📊 3. TÌNH TRẠNG COMMENTS")
    print("-" * 60)

    # Kiểm tra file comments chính
    comments_file = './data/raw/rakuten/simple_hotel_comments.csv'
    if os.path.exists(comments_file):
        try:
            df_comments = pd.read_csv(comments_file)
            print(f"💬 Comments trong file chính: {len(df_comments):,}")
        except:
            print("💬 Comments trong file chính: 0 (file trống)")

    # Kiểm tra các file comments riêng lẻ
    comment_files = glob.glob('./hotel_*_comments*.csv')
    if comment_files:
        print(f"📁 Tìm thấy {len(comment_files)} file comments riêng lẻ:")
        total_comments = 0
        for file in comment_files:
            try:
                df = pd.read_csv(file)
                total_comments += len(df)
                print(f"   - {os.path.basename(file)}: {len(df):,} comments")
            except:
                print(f"   - {os.path.basename(file)}: Error reading")
        print(f"💬 Tổng comments từ files riêng lẻ: {total_comments:,}")
    else:
        print("📁 Không tìm thấy file comments riêng lẻ")

    # 4. Đánh giá tiến độ
    print(f"\n📊 4. ĐÁNH GIÁ TIẾN ĐỘ CRAWL")
    print("-" * 60)

    try:
        if os.path.exists('./data/raw/rakuten/list_hotel_detail_url_file_unique.csv'):
            df_unique = pd.read_csv('./data/raw/rakuten/list_hotel_detail_url_file_unique.csv')
            total_hotels = len(df_unique)

            # Hotel details progress
            if os.path.exists('./data/raw/rakuten/detail_hotel_file.csv'):
                df_details = pd.read_csv('./data/raw/rakuten/detail_hotel_file.csv')
                details_progress = len(df_details) / total_hotels * 100
                print(f"🏨 Hotel Details: {len(df_details):,}/{total_hotels:,} ({details_progress:.1f}%)")

            # Comments progress (ước tính từ batch đã crawl)
            comments_crawled = 10  # Batch 1 đã crawl 10 hotels
            comments_progress = comments_crawled / total_hotels * 100
            print(f"💬 Comments: {comments_crawled:,}/{total_hotels:,} ({comments_progress:.1f}%)")

            print(f"\n🎯 TỔNG KẾT:")
            print(f"   ✅ Crawl cơ bản (areas, hotels): HOÀN THÀNH")
            print(f"   ✅ Hotel details: HOÀN THÀNH")
            print(f"   🔄 Comments: ĐANG TIẾN HÀNH ({comments_crawled}/{total_hotels} hotels)")

    except Exception as e:
        print(f"❌ Lỗi khi tính tiến độ: {str(e)}")

    # 5. Khuyến nghị
    print(f"\n📊 5. KHUYẾN NGHỊ TIẾP THEO")
    print("-" * 60)
    print("🔄 Tiếp tục crawl comments cho các hotels còn lại")
    print("📊 Có thể bắt đầu phân tích dữ liệu đã có")
    print("🧹 Làm sạch và chuẩn hóa dữ liệu")
    print("📈 Tạo dashboard để theo dõi tiến độ")

if __name__ == "__main__":
    generate_crawl_report()
