#!/bin/bash
# Check Incremental Crawler Status
# Auto-generated by setup_incremental_cron.sh

PROJECT_DIR="/var/www/micado-scraping"
LOG_DIR="$PROJECT_DIR/logs"

echo "📊 INCREMENTAL CRAWLER STATUS"
echo "=============================="

# Check if crawler is running
if pgrep -f "run_incremental_crawler" > /dev/null; then
    echo "🔄 Status: RUNNING"
    echo "💾 Memory: $(ps aux | grep run_incremental_crawler | grep -v grep | awk '{print $4}')%"
else
    echo "⏹️  Status: STOPPED"
fi

# Show latest log
if [ -f "$LOG_DIR/latest_incremental.log" ]; then
    echo ""
    echo "📋 LATEST LOG (last 10 lines):"
    echo "------------------------------"
    tail -10 "$LOG_DIR/latest_incremental.log"
else
    echo "❌ No log file found"
fi

# Show data statistics
echo ""
echo "📊 DATA STATISTICS:"
echo "-------------------"
cd "$PROJECT_DIR" || exit 1
python3 -c "
import pandas as pd
import os

files = {
    'Hotels': 'data/raw/rakuten/detail_hotel_file.csv',
    'Rooms': 'data/raw/rakuten/hotel_rooms_file.csv',
    'Plans': 'data/raw/rakuten/hotel_plans_file.csv'
}

for name, file_path in files.items():
    if os.path.exists(file_path):
        df = pd.read_csv(file_path)
        print(f'{name:8}: {len(df):,} records')
    else:
        print(f'{name:8}: File not found')
"
