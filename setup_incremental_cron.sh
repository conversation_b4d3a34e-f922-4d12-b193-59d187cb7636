#!/bin/bash
"""
Setup Cron Job for Incremental Rakuten Hotel Crawler
Configures daily automated crawling with incremental updates
"""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project directory
PROJECT_DIR="/var/www/micado-scraping"
LOG_DIR="$PROJECT_DIR/logs"

echo -e "${BLUE}🚀 SETTING UP INCREMENTAL CRAWLER CRON JOB${NC}"
echo "=================================================="

# Create logs directory if it doesn't exist
if [ ! -d "$LOG_DIR" ]; then
    mkdir -p "$LOG_DIR"
    echo -e "${GREEN}✅ Created logs directory: $LOG_DIR${NC}"
fi

# Create daily crawler script
DAILY_SCRIPT="$PROJECT_DIR/daily_incremental_crawler.sh"

cat > "$DAILY_SCRIPT" << 'EOF'
#!/bin/bash
# Daily Incremental Crawler Script
# Auto-generated by setup_incremental_cron.sh

# Set environment
export PATH="/usr/local/bin:/usr/bin:/bin"
PROJECT_DIR="/var/www/micado-scraping"
LOG_DIR="$PROJECT_DIR/logs"
DATE=$(date +"%Y%m%d_%H%M%S")

# Change to project directory
cd "$PROJECT_DIR" || exit 1

# Log file for this run
LOG_FILE="$LOG_DIR/incremental_crawler_$DATE.log"

echo "🚀 Starting Incremental Crawler - $(date)" >> "$LOG_FILE"
echo "=================================================" >> "$LOG_FILE"

# Run incremental crawler
python3 run_incremental_crawler.py --force-update-days 7 >> "$LOG_FILE" 2>&1
CRAWLER_EXIT_CODE=$?

echo "=================================================" >> "$LOG_FILE"
echo "✅ Crawler completed with exit code: $CRAWLER_EXIT_CODE - $(date)" >> "$LOG_FILE"

# If crawler succeeded, run import to database
if [ $CRAWLER_EXIT_CODE -eq 0 ]; then
    echo "🔄 Starting database import..." >> "$LOG_FILE"
    python3 scripts/daily/enhanced_hotel_import.py --mode incremental >> "$LOG_FILE" 2>&1
    IMPORT_EXIT_CODE=$?
    echo "✅ Import completed with exit code: $IMPORT_EXIT_CODE - $(date)" >> "$LOG_FILE"
else
    echo "❌ Crawler failed, skipping database import" >> "$LOG_FILE"
fi

# Clean up old logs (keep last 30 days)
find "$LOG_DIR" -name "incremental_crawler_*.log" -mtime +30 -delete

# Create symlink to latest log
ln -sf "$LOG_FILE" "$LOG_DIR/latest_incremental.log"

echo "📊 Daily incremental crawler completed - $(date)" >> "$LOG_FILE"
EOF

# Make script executable
chmod +x "$DAILY_SCRIPT"
echo -e "${GREEN}✅ Created daily script: $DAILY_SCRIPT${NC}"

# Create cron job entry
CRON_ENTRY="0 2 * * * $DAILY_SCRIPT"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "$DAILY_SCRIPT"; then
    echo -e "${YELLOW}⚠️  Cron job already exists${NC}"
else
    # Add cron job
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
    echo -e "${GREEN}✅ Added cron job: $CRON_ENTRY${NC}"
fi

# Create monitoring script for cron
MONITOR_SCRIPT="$PROJECT_DIR/check_incremental_status.sh"

cat > "$MONITOR_SCRIPT" << 'EOF'
#!/bin/bash
# Check Incremental Crawler Status
# Auto-generated by setup_incremental_cron.sh

PROJECT_DIR="/var/www/micado-scraping"
LOG_DIR="$PROJECT_DIR/logs"

echo "📊 INCREMENTAL CRAWLER STATUS"
echo "=============================="

# Check if crawler is running
if pgrep -f "run_incremental_crawler" > /dev/null; then
    echo "🔄 Status: RUNNING"
    echo "💾 Memory: $(ps aux | grep run_incremental_crawler | grep -v grep | awk '{print $4}')%"
else
    echo "⏹️  Status: STOPPED"
fi

# Show latest log
if [ -f "$LOG_DIR/latest_incremental.log" ]; then
    echo ""
    echo "📋 LATEST LOG (last 10 lines):"
    echo "------------------------------"
    tail -10 "$LOG_DIR/latest_incremental.log"
else
    echo "❌ No log file found"
fi

# Show data statistics
echo ""
echo "📊 DATA STATISTICS:"
echo "-------------------"
cd "$PROJECT_DIR" || exit 1
python3 -c "
import pandas as pd
import os

files = {
    'Hotels': 'data/raw/rakuten/detail_hotel_file.csv',
    'Rooms': 'data/raw/rakuten/hotel_rooms_file.csv',
    'Plans': 'data/raw/rakuten/hotel_plans_file.csv'
}

for name, file_path in files.items():
    if os.path.exists(file_path):
        df = pd.read_csv(file_path)
        print(f'{name:8}: {len(df):,} records')
    else:
        print(f'{name:8}: File not found')
"
EOF

chmod +x "$MONITOR_SCRIPT"
echo -e "${GREEN}✅ Created monitoring script: $MONITOR_SCRIPT${NC}"

# Show summary
echo ""
echo -e "${BLUE}📋 SETUP SUMMARY${NC}"
echo "================="
echo -e "${GREEN}✅ Daily script:${NC} $DAILY_SCRIPT"
echo -e "${GREEN}✅ Monitor script:${NC} $MONITOR_SCRIPT"
echo -e "${GREEN}✅ Cron schedule:${NC} Daily at 2:00 AM"
echo -e "${GREEN}✅ Log directory:${NC} $LOG_DIR"

echo ""
echo -e "${BLUE}🔧 USAGE COMMANDS${NC}"
echo "=================="
echo "Check status:     $MONITOR_SCRIPT"
echo "View logs:        tail -f $LOG_DIR/latest_incremental.log"
echo "Run manually:     $DAILY_SCRIPT"
echo "List cron jobs:   crontab -l"

echo ""
echo -e "${GREEN}🎉 Incremental crawler cron job setup completed!${NC}"
